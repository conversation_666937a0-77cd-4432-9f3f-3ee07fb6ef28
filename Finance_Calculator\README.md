# Personal Finance Dashboard

A comprehensive web-based personal finance management tool with a spreadsheet-like interface for tracking income and expenses.

## Features

### 📊 Transaction Management
- **Grid-style interface** similar to Excel/Google Sheets
- **Add, edit, and delete** transactions with ease
- **Real-time validation** with helpful error messages
- **Sortable columns** - click any column header to sort
- **Sample data** included for first-time users

### 💰 Transaction Fields
- **Date** - Transaction date with validation
- **Type** - Credit (Income) or Debit (Expense)
- **Amount** - Monetary value with validation (max $1,000,000)
- **Comment** - Description or notes about the transaction
- **Tags** - Categorize by person (<PERSON><PERSON>, <PERSON>, <PERSON>)

### 🔍 Search & Filter
- **Search** transactions by comment, type, or tags
- **Filter by tag** - View transactions for specific people
- **Filter by date** - Focus on specific dates
- **Real-time filtering** as you type

### 📈 Summary & Analytics
- **Live statistics** showing total income, expenses, and net amount
- **Summary dashboard** with breakdowns by:
  - Tags (<PERSON><PERSON>, <PERSON>, <PERSON>)
  - Monthly overview
- **Color-coded amounts** (green for income, red for expenses)

### 💾 Data Management
- **Automatic saving** to browser's local storage
- **Export to CSV** for backup or external analysis
- **Clear all data** with double confirmation
- **Data persistence** across browser sessions

### ⌨️ Keyboard Shortcuts
- **Ctrl+N** (Cmd+N on Mac) - Add new transaction
- **Ctrl+E** (Cmd+E on Mac) - Export data to CSV
- **Escape** - Close modal/dialog

### 📱 Responsive Design
- **Mobile-friendly** interface
- **Touch-optimized** buttons and controls
- **Adaptive layout** for different screen sizes

## Getting Started

1. **Open the dashboard** by opening `index.html` in your web browser
2. **Explore sample data** - The dashboard comes with example transactions
3. **Add your first transaction** using the "Add Transaction" button
4. **Use filters and search** to organize your data
5. **View summaries** in the "Summary" tab

## File Structure

```
Finance_Calculator/
├── index.html      # Main HTML structure
├── styles.css      # Styling and responsive design
├── script.js       # JavaScript functionality
└── README.md       # This documentation
```

## Browser Compatibility

- ✅ Chrome (recommended)
- ✅ Firefox
- ✅ Safari
- ✅ Edge
- ⚠️ Internet Explorer (limited support)

## Data Storage

- All data is stored locally in your browser's localStorage
- No data is sent to external servers
- Export your data regularly for backup
- Clearing browser data will remove all transactions

## Tips for Best Use

1. **Regular backups** - Export your data monthly
2. **Consistent tagging** - Use the same tags for better organization
3. **Detailed comments** - Add meaningful descriptions for easier searching
4. **Date accuracy** - Enter transactions promptly for accurate tracking

## Troubleshooting

**Data not saving?**
- Check if localStorage is enabled in your browser
- Ensure you're not in private/incognito mode

**Performance issues?**
- The dashboard handles thousands of transactions efficiently
- Consider exporting and archiving old data if needed

**Mobile display issues?**
- The interface is optimized for screens 320px and wider
- Use landscape mode on small devices for better experience

## Future Enhancements

Potential features for future versions:
- Import from CSV/bank statements
- Budget tracking and alerts
- Charts and graphs
- Multi-currency support
- Recurring transaction templates
- Data synchronization across devices

---

**Version:** 1.0  
**Last Updated:** November 2024  
**License:** Free for personal use
