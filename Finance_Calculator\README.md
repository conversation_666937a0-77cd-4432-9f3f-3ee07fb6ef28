# Personal Finance Dashboard

A comprehensive web-based personal finance management tool with a spreadsheet-like interface for tracking income and expenses.

## Features

### 📊 Transaction Management
- **Grid-style interface** similar to Excel/Google Sheets
- **Add, edit, and delete** transactions with ease
- **Real-time validation** with helpful error messages
- **Sortable columns** - click any column header to sort
- **Sample data** included for first-time users

### 💰 Transaction Fields
- **Date** - Transaction date with validation
- **Type** - Credit (Income) or Debit (Expense)
- **Amount** - Monetary value with validation (max $1,000,000)
- **Comment** - Description or notes about the transaction
- **Tags** - Categorize by person (<PERSON><PERSON>, <PERSON>, <PERSON>)

### 🔍 Search & Filter
- **Search** transactions by comment, type, or tags
- **Filter by tag** - View transactions for specific people
- **Filter by date** - Focus on specific dates
- **Real-time filtering** as you type

### 📈 Summary & Analytics
- **Live statistics** showing total income, expenses, and net amount
- **Summary dashboard** with breakdowns by:
  - Tags (<PERSON><PERSON>, <PERSON>, <PERSON>)
  - Monthly overview
- **Color-coded amounts** (green for income, red for expenses)

### 💾 Data Management
- **Automatic saving** to browser's local storage
- **Export to CSV** for backup or external analysis
- **Clear all data** with double confirmation
- **Data persistence** across browser sessions

### 🎨 Theme Support
- **Dark/Light theme toggle** - Switch between themes with one click
- **Automatic theme persistence** - Your preference is saved
- **System-friendly colors** - Easy on the eyes in any lighting

### 📝 Grid Editing Mode
- **SharePoint-style editing** - Edit multiple rows directly in the grid
- **Inline editing** - Click "Edit Grid" to enable inline editing
- **Bulk operations** - Save all changes at once or cancel all
- **Add new rows** - Add transactions directly in the grid
- **Individual row controls** - Save or cancel changes per row

### ⌨️ Keyboard Shortcuts
- **Ctrl+N** (Cmd+N on Mac) - Add new transaction
- **Ctrl+E** (Cmd+E on Mac) - Export data to CSV
- **Escape** - Close modal/dialog

### 📱 Responsive Design
- **Mobile-friendly** interface
- **Touch-optimized** buttons and controls
- **Adaptive layout** for different screen sizes
- **Dark theme support** on all devices

## Getting Started

1. **Open the dashboard** by opening `index.html` in your web browser
2. **Choose your theme** - Click the moon/sun icon to toggle dark/light theme
3. **Explore sample data** - The dashboard comes with example transactions
4. **Try both editing modes**:
   - **Modal editing**: Click "Add Transaction" for the traditional form
   - **Grid editing**: Click "Edit Grid" for SharePoint-style inline editing
5. **Use filters and search** to organize your data
6. **View summaries** in the "Summary" tab

## File Structure

```
Finance_Calculator/
├── index.html      # Main HTML structure
├── styles.css      # Styling and responsive design
├── script.js       # JavaScript functionality
└── README.md       # This documentation
```

## Browser Compatibility

- ✅ Chrome (recommended)
- ✅ Firefox
- ✅ Safari
- ✅ Edge
- ⚠️ Internet Explorer (limited support)

## Data Storage

- All data is stored locally in your browser's localStorage
- No data is sent to external servers
- Export your data regularly for backup
- Clearing browser data will remove all transactions

## Editing Modes

### Modal Editing (Traditional)
- Click "Add Transaction" button
- Fill out the form in a popup modal
- Best for: Adding single transactions with validation

### Grid Editing (SharePoint-style)
- Click "Edit Grid" to enter edit mode
- Edit multiple rows directly in the table
- Use "Add Row" to create new transactions inline
- "Save All" to commit all changes at once
- "Cancel" to discard all changes
- Best for: Bulk editing and data entry

## Tips for Best Use

1. **Regular backups** - Export your data monthly
2. **Consistent tagging** - Use the same tags for better organization
3. **Detailed comments** - Add meaningful descriptions for easier searching
4. **Date accuracy** - Enter transactions promptly for accurate tracking
5. **Use grid mode** - For entering multiple transactions quickly
6. **Dark theme** - Easier on the eyes during extended use

## Troubleshooting

**Data not saving?**
- Check if localStorage is enabled in your browser
- Ensure you're not in private/incognito mode

**Performance issues?**
- The dashboard handles thousands of transactions efficiently
- Consider exporting and archiving old data if needed

**Mobile display issues?**
- The interface is optimized for screens 320px and wider
- Use landscape mode on small devices for better experience

## Future Enhancements

Potential features for future versions:
- Import from CSV/bank statements
- Budget tracking and alerts
- Charts and graphs
- Multi-currency support
- Recurring transaction templates
- Data synchronization across devices

---

**Version:** 1.0  
**Last Updated:** November 2024  
**License:** Free for personal use
