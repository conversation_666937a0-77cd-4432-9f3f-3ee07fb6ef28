<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Finance Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1><i class="fas fa-chart-line"></i> Personal Finance Dashboard</h1>
            <div class="header-actions">
                <button class="btn btn-icon" id="themeToggle" title="Toggle dark/light theme">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
                <button class="btn btn-primary" id="addTransactionBtn" title="Add new transaction (Ctrl+N)">
                    <i class="fas fa-plus"></i> Add Transaction
                </button>
                <button class="btn btn-secondary" id="exportBtn" title="Export to CSV (Ctrl+E)">
                    <i class="fas fa-download"></i> Export
                </button>
                <button class="btn btn-danger" id="clearDataBtn" title="Clear all data">
                    <i class="fas fa-trash-alt"></i> Clear All
                </button>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="nav-tabs">
            <button class="nav-tab active" data-tab="transactions">
                <i class="fas fa-exchange-alt"></i> Transactions
            </button>
            <button class="nav-tab" data-tab="summary">
                <i class="fas fa-chart-pie"></i> Summary
            </button>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Transactions Tab -->
            <div id="transactions" class="tab-content active">
                <div class="toolbar">
                    <div class="search-filter">
                        <input type="text" id="searchInput" placeholder="Search transactions..." class="search-input">
                        <select id="tagFilter" class="filter-select">
                            <option value="">All Tags</option>
                            <option value="Nikita">Nikita</option>
                            <option value="Lance">Lance</option>
                            <option value="Both">Both</option>
                        </select>
                        <input type="date" id="dateFilter" class="filter-date">
                    </div>
                    <div class="grid-controls">
                        <button class="btn btn-secondary" id="toggleEditMode" title="Toggle grid edit mode">
                            <i class="fas fa-edit"></i> <span id="editModeText">Edit Grid</span>
                        </button>
                        <div class="edit-mode-controls" id="editModeControls" style="display: none;">
                            <button class="btn btn-success" id="saveAllBtn" title="Save all changes">
                                <i class="fas fa-save"></i> Save All
                            </button>
                            <button class="btn btn-primary" id="addRowBtn" title="Add new row">
                                <i class="fas fa-plus"></i> Add Row
                            </button>
                            <button class="btn btn-warning" id="cancelAllBtn" title="Cancel all changes">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                        </div>
                    </div>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <span class="stat-label">Total Income:</span>
                            <span class="stat-value income" id="totalIncome">$0.00</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Expenses:</span>
                            <span class="stat-value expense" id="totalExpenses">$0.00</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Net:</span>
                            <span class="stat-value" id="netAmount">$0.00</span>
                        </div>
                    </div>
                </div>

                <!-- Transaction Grid -->
                <div class="grid-container">
                    <table class="transaction-grid" id="transactionGrid">
                        <thead>
                            <tr>
                                <th class="sortable" data-column="date">
                                    Date <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="type">
                                    Type <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="amount">
                                    Amount <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="comment">
                                    Comment <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-column="tags">
                                    Tags <i class="fas fa-sort"></i>
                                </th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="transactionTableBody">
                            <!-- Transactions will be populated here -->
                        </tbody>
                    </table>
                </div>

                <!-- Add/Edit Transaction Form -->
                <div class="modal" id="transactionModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 id="modalTitle">Add Transaction</h3>
                            <button class="close-btn" id="closeModal">&times;</button>
                        </div>
                        <form id="transactionForm">
                            <div class="form-group">
                                <label for="transactionDate">Date:</label>
                                <input type="date" id="transactionDate" required>
                            </div>
                            <div class="form-group">
                                <label for="transactionType">Type:</label>
                                <select id="transactionType" required>
                                    <option value="">Select Type</option>
                                    <option value="credit">Credit (Income)</option>
                                    <option value="debit">Debit (Expense)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="transactionAmount">Amount:</label>
                                <input type="number" id="transactionAmount" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="transactionComment">Comment:</label>
                                <input type="text" id="transactionComment" placeholder="Description of transaction">
                            </div>
                            <div class="form-group">
                                <label for="transactionTags">Tags:</label>
                                <select id="transactionTags" required>
                                    <option value="">Select Tag</option>
                                    <option value="Nikita">Nikita</option>
                                    <option value="Lance">Lance</option>
                                    <option value="Both">Both</option>
                                </select>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="cancelBtn">Cancel</button>
                                <button type="submit" class="btn btn-primary">Save Transaction</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Summary Tab -->
            <div id="summary" class="tab-content">
                <div class="summary-dashboard">
                    <h2>Financial Summary</h2>
                    <div class="summary-cards">
                        <div class="summary-card">
                            <h3>By Tag</h3>
                            <div id="tagSummary"></div>
                        </div>
                        <div class="summary-card">
                            <h3>Monthly Overview</h3>
                            <div id="monthlySummary"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
