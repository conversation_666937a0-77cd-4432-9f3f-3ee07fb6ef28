// Personal Finance Dashboard JavaScript

class FinanceDashboard {
    constructor() {
        this.transactions = this.loadTransactions();
        this.categories = this.loadCategories();
        this.tags = this.loadTags();
        this.currentEditId = null;
        this.currentCategoryId = null;
        this.currentTagId = null;
        this.sortColumn = 'date';
        this.sortDirection = 'desc';
        this.isEditMode = false;
        this.editingRows = new Set();
        this.originalData = new Map();

        this.initializeTheme();
        this.initializeEventListeners();
        this.populateDropdowns();
        this.renderTransactions();
        this.renderCategories();
        this.renderTags();
        this.updateSummaryStats();
        this.setupTabNavigation();
    }

    // Initialize theme
    initializeTheme() {
        const savedTheme = localStorage.getItem('financeTheme') || 'light';
        this.setTheme(savedTheme);
    }

    // Set theme
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        const themeIcon = document.getElementById('themeIcon');
        if (themeIcon) {
            themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }
        localStorage.setItem('financeTheme', theme);
    }

    // Toggle theme
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }

    // Initialize all event listeners
    initializeEventListeners() {
        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());

        // Modal controls
        document.getElementById('addTransactionBtn').addEventListener('click', () => this.openModal());
        document.getElementById('closeModal').addEventListener('click', () => this.closeModal());
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeModal());

        // Form submission
        document.getElementById('transactionForm').addEventListener('submit', (e) => this.handleFormSubmit(e));

        // Category management
        document.getElementById('addCategoryBtn').addEventListener('click', () => this.openCategoryModal());
        document.getElementById('closeCategoryModal').addEventListener('click', () => this.closeCategoryModal());
        document.getElementById('cancelCategoryBtn').addEventListener('click', () => this.closeCategoryModal());
        document.getElementById('categoryForm').addEventListener('submit', (e) => this.handleCategorySubmit(e));

        // Tag management
        document.getElementById('addTagBtn').addEventListener('click', () => this.openTagModal());
        document.getElementById('closeTagModal').addEventListener('click', () => this.closeTagModal());
        document.getElementById('cancelTagBtn').addEventListener('click', () => this.closeTagModal());
        document.getElementById('tagForm').addEventListener('submit', (e) => this.handleTagSubmit(e));

        // Grid edit mode controls
        document.getElementById('toggleEditMode').addEventListener('click', () => this.toggleEditMode());
        document.getElementById('saveAllBtn').addEventListener('click', () => this.saveAllChanges());
        document.getElementById('addRowBtn').addEventListener('click', () => this.addNewRow());
        document.getElementById('cancelAllBtn').addEventListener('click', () => this.cancelAllChanges());

        // Search and filters
        document.getElementById('searchInput').addEventListener('input', () => this.filterTransactions());
        document.getElementById('categoryFilter').addEventListener('change', () => this.filterTransactions());
        document.getElementById('recurringFilter').addEventListener('change', () => this.filterTransactions());
        document.getElementById('tagFilter').addEventListener('change', () => this.filterTransactions());
        document.getElementById('dateFilter').addEventListener('change', () => this.filterTransactions());

        // Export functionality
        document.getElementById('exportBtn').addEventListener('click', () => this.exportData());

        // Clear data functionality
        document.getElementById('clearDataBtn').addEventListener('click', () => this.clearAllData());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        // Close modal when clicking outside
        document.getElementById('transactionModal').addEventListener('click', (e) => {
            if (e.target.id === 'transactionModal') {
                this.closeModal();
            }
        });

        // Column sorting
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => {
                const column = header.dataset.column;
                this.sortTransactions(column);
            });
        });
    }

    // Setup tab navigation
    setupTabNavigation() {
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                this.switchTab(tabName);
            });
        });
    }

    // Switch between tabs
    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        // Update summary if switching to summary tab
        if (tabName === 'summary') {
            this.updateSummaryDashboard();
        }
    }

    // Load transactions from localStorage
    loadTransactions() {
        const stored = localStorage.getItem('financeTransactions');
        if (stored) {
            return JSON.parse(stored);
        } else {
            // Return sample data for first-time users
            return this.getSampleData();
        }
    }

    // Get sample data for demonstration
    getSampleData() {
        const today = new Date();
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        const twoWeeksAgo = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 14);

        return [
            {
                id: '1',
                date: today.toISOString().split('T')[0],
                type: 'credit',
                amount: 3500.00,
                category: 'Income',
                recurring: 'Yes',
                comment: 'Salary deposit',
                tags: 'Both'
            },
            {
                id: '2',
                date: twoWeeksAgo.toISOString().split('T')[0],
                type: 'debit',
                amount: 1200.00,
                category: 'Bills & Utilities',
                recurring: 'Yes',
                comment: 'Rent payment',
                tags: 'Both'
            },
            {
                id: '3',
                date: lastMonth.toISOString().split('T')[0],
                type: 'debit',
                amount: 85.50,
                category: 'Food & Dining',
                recurring: 'No',
                comment: 'Grocery shopping',
                tags: 'Nikita'
            },
            {
                id: '4',
                date: lastMonth.toISOString().split('T')[0],
                type: 'debit',
                amount: 45.00,
                category: 'Transportation',
                recurring: 'No',
                comment: 'Gas station',
                tags: 'Lance'
            },
            {
                id: '5',
                date: new Date(today.getFullYear(), today.getMonth(), today.getDate() - 3).toISOString().split('T')[0],
                type: 'credit',
                amount: 250.00,
                category: 'Income',
                recurring: 'No',
                comment: 'Freelance work',
                tags: 'Lance'
            }
        ];
    }

    // Populate dropdown options
    populateDropdowns() {
        this.populateCategoryDropdown();
        this.populateTagDropdown();
        this.populateFilterDropdowns();
    }

    // Populate category dropdown
    populateCategoryDropdown() {
        const categorySelect = document.getElementById('transactionCategory');
        categorySelect.innerHTML = '<option value="">Select Category</option>';

        this.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.name;
            option.textContent = category.name;
            categorySelect.appendChild(option);
        });
    }

    // Populate tag dropdown
    populateTagDropdown() {
        const tagSelect = document.getElementById('transactionTags');
        tagSelect.innerHTML = '<option value="">Select Tag</option>';

        this.tags.forEach(tag => {
            const option = document.createElement('option');
            option.value = tag.name;
            option.textContent = tag.name;
            tagSelect.appendChild(option);
        });
    }

    // Populate filter dropdowns
    populateFilterDropdowns() {
        // Category filter
        const categoryFilter = document.getElementById('categoryFilter');
        categoryFilter.innerHTML = '<option value="">All Categories</option>';
        this.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.name;
            option.textContent = category.name;
            categoryFilter.appendChild(option);
        });

        // Tag filter
        const tagFilter = document.getElementById('tagFilter');
        tagFilter.innerHTML = '<option value="">All Tags</option>';
        this.tags.forEach(tag => {
            const option = document.createElement('option');
            option.value = tag.name;
            option.textContent = tag.name;
            tagFilter.appendChild(option);
        });
    }

    // Save transactions to localStorage
    saveTransactions() {
        localStorage.setItem('financeTransactions', JSON.stringify(this.transactions));
    }

    // Load categories from localStorage
    loadCategories() {
        const stored = localStorage.getItem('financeCategories');
        if (stored) {
            return JSON.parse(stored);
        } else {
            return this.getDefaultCategories();
        }
    }

    // Save categories to localStorage
    saveCategories() {
        localStorage.setItem('financeCategories', JSON.stringify(this.categories));
    }

    // Load tags from localStorage
    loadTags() {
        const stored = localStorage.getItem('financeTags');
        if (stored) {
            return JSON.parse(stored);
        } else {
            return this.getDefaultTags();
        }
    }

    // Save tags to localStorage
    saveTags() {
        localStorage.setItem('financeTags', JSON.stringify(this.tags));
    }

    // Get default categories
    getDefaultCategories() {
        return [
            { id: '1', name: 'Food & Dining', description: 'Restaurants, groceries, etc.', color: '#ff6b6b' },
            { id: '2', name: 'Transportation', description: 'Gas, public transport, etc.', color: '#4ecdc4' },
            { id: '3', name: 'Shopping', description: 'Clothing, electronics, etc.', color: '#45b7d1' },
            { id: '4', name: 'Entertainment', description: 'Movies, games, etc.', color: '#f9ca24' },
            { id: '5', name: 'Bills & Utilities', description: 'Rent, electricity, etc.', color: '#f0932b' },
            { id: '6', name: 'Income', description: 'Salary, freelance, etc.', color: '#6c5ce7' },
            { id: '7', name: 'Healthcare', description: 'Medical expenses', color: '#a29bfe' },
            { id: '8', name: 'Other', description: 'Miscellaneous expenses', color: '#636e72' }
        ];
    }

    // Get default tags
    getDefaultTags() {
        return [
            { id: '1', name: 'Nikita', description: 'Nikita\'s expenses', color: '#e3f2fd' },
            { id: '2', name: 'Lance', description: 'Lance\'s expenses', color: '#f3e5f5' },
            { id: '3', name: 'Both', description: 'Shared expenses', color: '#e8f5e8' }
        ];
    }

    // Open modal for adding/editing transaction
    openModal(transaction = null) {
        const modal = document.getElementById('transactionModal');
        const form = document.getElementById('transactionForm');
        const title = document.getElementById('modalTitle');

        if (transaction) {
            // Edit mode
            title.textContent = 'Edit Transaction';
            this.currentEditId = transaction.id;
            
            document.getElementById('transactionDate').value = transaction.date;
            document.getElementById('transactionType').value = transaction.type;
            document.getElementById('transactionAmount').value = transaction.amount;
            document.getElementById('transactionCategory').value = transaction.category || '';
            document.getElementById('transactionRecurring').value = transaction.recurring || '';
            document.getElementById('transactionComment').value = transaction.comment;
            document.getElementById('transactionTags').value = transaction.tags;
        } else {
            // Add mode
            title.textContent = 'Add Transaction';
            this.currentEditId = null;
            form.reset();
            
            // Set today's date as default
            document.getElementById('transactionDate').value = new Date().toISOString().split('T')[0];
        }

        modal.style.display = 'block';
    }

    // Close modal
    closeModal() {
        document.getElementById('transactionModal').style.display = 'none';
        this.currentEditId = null;
    }

    // Category modal management
    openCategoryModal(category = null) {
        const modal = document.getElementById('categoryModal');
        const form = document.getElementById('categoryForm');
        const title = document.getElementById('categoryModalTitle');

        if (category) {
            title.textContent = 'Edit Category';
            this.currentCategoryId = category.id;
            document.getElementById('categoryName').value = category.name;
            document.getElementById('categoryDescription').value = category.description;
            document.getElementById('categoryColor').value = category.color;
        } else {
            title.textContent = 'Add Category';
            this.currentCategoryId = null;
            form.reset();
            document.getElementById('categoryColor').value = '#667eea';
        }

        modal.style.display = 'block';
    }

    closeCategoryModal() {
        document.getElementById('categoryModal').style.display = 'none';
        this.currentCategoryId = null;
    }

    // Tag modal management
    openTagModal(tag = null) {
        const modal = document.getElementById('tagModal');
        const form = document.getElementById('tagForm');
        const title = document.getElementById('tagModalTitle');

        if (tag) {
            title.textContent = 'Edit Tag';
            this.currentTagId = tag.id;
            document.getElementById('tagName').value = tag.name;
            document.getElementById('tagDescription').value = tag.description;
            document.getElementById('tagColor').value = tag.color;
        } else {
            title.textContent = 'Add Tag';
            this.currentTagId = null;
            form.reset();
            document.getElementById('tagColor').value = '#28a745';
        }

        modal.style.display = 'block';
    }

    closeTagModal() {
        document.getElementById('tagModal').style.display = 'none';
        this.currentTagId = null;
    }

    // Handle form submission
    handleFormSubmit(e) {
        e.preventDefault();

        // Validate form data
        const validation = this.validateFormData();
        if (!validation.isValid) {
            this.showValidationErrors(validation.errors);
            return;
        }

        const formData = {
            date: document.getElementById('transactionDate').value,
            type: document.getElementById('transactionType').value,
            amount: parseFloat(document.getElementById('transactionAmount').value),
            category: document.getElementById('transactionCategory').value,
            recurring: document.getElementById('transactionRecurring').value,
            comment: document.getElementById('transactionComment').value.trim(),
            tags: document.getElementById('transactionTags').value
        };

        if (this.currentEditId) {
            // Update existing transaction
            const index = this.transactions.findIndex(t => t.id === this.currentEditId);
            if (index !== -1) {
                this.transactions[index] = { ...formData, id: this.currentEditId };
            }
        } else {
            // Add new transaction
            const newTransaction = {
                ...formData,
                id: Date.now().toString()
            };
            this.transactions.push(newTransaction);
        }

        this.saveTransactions();
        this.renderTransactions();
        this.updateSummaryStats();
        this.closeModal();
        this.showSuccessMessage(this.currentEditId ? 'Transaction updated successfully!' : 'Transaction added successfully!');
    }

    // Handle category form submission
    handleCategorySubmit(e) {
        e.preventDefault();

        const formData = {
            name: document.getElementById('categoryName').value.trim(),
            description: document.getElementById('categoryDescription').value.trim(),
            color: document.getElementById('categoryColor').value
        };

        // Validate
        if (!formData.name) {
            alert('Category name is required');
            return;
        }

        // Check for duplicate names (excluding current category if editing)
        const existingCategory = this.categories.find(cat =>
            cat.name.toLowerCase() === formData.name.toLowerCase() &&
            cat.id !== this.currentCategoryId
        );

        if (existingCategory) {
            alert('A category with this name already exists');
            return;
        }

        if (this.currentCategoryId) {
            // Update existing category
            const index = this.categories.findIndex(cat => cat.id === this.currentCategoryId);
            if (index !== -1) {
                this.categories[index] = { ...this.categories[index], ...formData };
            }
        } else {
            // Add new category
            const newCategory = {
                ...formData,
                id: Date.now().toString()
            };
            this.categories.push(newCategory);
        }

        this.saveCategories();
        this.populateCategoryDropdown();
        this.populateFilterDropdowns();
        this.renderCategories();
        this.closeCategoryModal();
        this.showSuccessMessage(this.currentCategoryId ? 'Category updated successfully!' : 'Category added successfully!');
    }

    // Handle tag form submission
    handleTagSubmit(e) {
        e.preventDefault();

        const formData = {
            name: document.getElementById('tagName').value.trim(),
            description: document.getElementById('tagDescription').value.trim(),
            color: document.getElementById('tagColor').value
        };

        // Validate
        if (!formData.name) {
            alert('Tag name is required');
            return;
        }

        // Check for duplicate names (excluding current tag if editing)
        const existingTag = this.tags.find(tag =>
            tag.name.toLowerCase() === formData.name.toLowerCase() &&
            tag.id !== this.currentTagId
        );

        if (existingTag) {
            alert('A tag with this name already exists');
            return;
        }

        if (this.currentTagId) {
            // Update existing tag
            const index = this.tags.findIndex(tag => tag.id === this.currentTagId);
            if (index !== -1) {
                this.tags[index] = { ...this.tags[index], ...formData };
            }
        } else {
            // Add new tag
            const newTag = {
                ...formData,
                id: Date.now().toString()
            };
            this.tags.push(newTag);
        }

        this.saveTags();
        this.populateTagDropdown();
        this.populateFilterDropdowns();
        this.renderTags();
        this.closeTagModal();
        this.showSuccessMessage(this.currentTagId ? 'Tag updated successfully!' : 'Tag added successfully!');
    }

    // Render categories table
    renderCategories() {
        const tbody = document.getElementById('categoryTableBody');
        tbody.innerHTML = '';

        this.categories.forEach(category => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${category.name}</td>
                <td>${category.description}</td>
                <td>
                    <span class="color-preview" style="background-color: ${category.color}"></span>
                    ${category.color}
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" onclick="dashboard.openCategoryModal(${JSON.stringify(category).replace(/"/g, '&quot;')})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="dashboard.deleteCategory('${category.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Render tags table
    renderTags() {
        const tbody = document.getElementById('tagTableBody');
        tbody.innerHTML = '';

        this.tags.forEach(tag => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${tag.name}</td>
                <td>${tag.description}</td>
                <td>
                    <span class="color-preview" style="background-color: ${tag.color}"></span>
                    ${tag.color}
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" onclick="dashboard.openTagModal(${JSON.stringify(tag).replace(/"/g, '&quot;')})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="dashboard.deleteTag('${tag.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Delete category
    deleteCategory(id) {
        const category = this.categories.find(cat => cat.id === id);
        if (!category) return;

        // Check if category is being used in transactions
        const usedInTransactions = this.transactions.some(t => t.category === category.name);
        if (usedInTransactions) {
            alert(`Cannot delete category "${category.name}" because it is being used in transactions.`);
            return;
        }

        if (confirm(`Are you sure you want to delete the category "${category.name}"?`)) {
            this.categories = this.categories.filter(cat => cat.id !== id);
            this.saveCategories();
            this.populateCategoryDropdown();
            this.populateFilterDropdowns();
            this.renderCategories();
            this.showSuccessMessage('Category deleted successfully!');
        }
    }

    // Delete tag
    deleteTag(id) {
        const tag = this.tags.find(t => t.id === id);
        if (!tag) return;

        // Check if tag is being used in transactions
        const usedInTransactions = this.transactions.some(t => t.tags === tag.name);
        if (usedInTransactions) {
            alert(`Cannot delete tag "${tag.name}" because it is being used in transactions.`);
            return;
        }

        if (confirm(`Are you sure you want to delete the tag "${tag.name}"?`)) {
            this.tags = this.tags.filter(t => t.id !== id);
            this.saveTags();
            this.populateTagDropdown();
            this.populateFilterDropdowns();
            this.renderTags();
            this.showSuccessMessage('Tag deleted successfully!');
        }
    }

    // Validate form data
    validateFormData() {
        const errors = [];

        const date = document.getElementById('transactionDate').value;
        const type = document.getElementById('transactionType').value;
        const amount = document.getElementById('transactionAmount').value;
        const category = document.getElementById('transactionCategory').value;
        const recurring = document.getElementById('transactionRecurring').value;
        const tags = document.getElementById('transactionTags').value;

        // Date validation
        if (!date) {
            errors.push('Date is required');
        } else {
            const selectedDate = new Date(date);
            const today = new Date();
            const oneYearFromNow = new Date();
            oneYearFromNow.setFullYear(today.getFullYear() + 1);

            if (selectedDate > oneYearFromNow) {
                errors.push('Date cannot be more than one year in the future');
            }
        }

        // Type validation
        if (!type) {
            errors.push('Transaction type is required');
        }

        // Amount validation
        if (!amount) {
            errors.push('Amount is required');
        } else {
            const numAmount = parseFloat(amount);
            if (isNaN(numAmount) || numAmount <= 0) {
                errors.push('Amount must be a positive number');
            } else if (numAmount > 1000000) {
                errors.push('Amount cannot exceed $1,000,000');
            }
        }

        // Category validation
        if (!category) {
            errors.push('Category is required');
        }

        // Recurring validation
        if (!recurring) {
            errors.push('Recurring option is required');
        }

        // Tags validation
        if (!tags) {
            errors.push('Tag is required');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // Show validation errors
    showValidationErrors(errors) {
        this.removeExistingMessages();

        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-errors';
        errorDiv.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Please fix the following errors:</strong>
                <ul>
                    ${errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
            </div>
        `;

        const form = document.getElementById('transactionForm');
        form.insertBefore(errorDiv, form.firstChild);
    }

    // Show success message
    showSuccessMessage(message) {
        this.removeExistingMessages();

        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.innerHTML = `
            <div class="success-content">
                <i class="fas fa-check-circle"></i>
                ${message}
            </div>
        `;

        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(successDiv, mainContent.firstChild);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    }

    // Remove existing messages
    removeExistingMessages() {
        const existingErrors = document.querySelector('.validation-errors');
        const existingSuccess = document.querySelector('.success-message');

        if (existingErrors) {
            existingErrors.parentNode.removeChild(existingErrors);
        }
        if (existingSuccess) {
            existingSuccess.parentNode.removeChild(existingSuccess);
        }
    }

    // Delete transaction
    deleteTransaction(id) {
        if (confirm('Are you sure you want to delete this transaction?')) {
            this.transactions = this.transactions.filter(t => t.id !== id);
            this.saveTransactions();
            this.renderTransactions();
            this.updateSummaryStats();
        }
    }

    // Sort transactions
    sortTransactions(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }

        this.transactions.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            // Handle different data types
            if (column === 'date') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            } else if (column === 'amount') {
                aVal = parseFloat(aVal);
                bVal = parseFloat(bVal);
            } else {
                aVal = aVal.toString().toLowerCase();
                bVal = bVal.toString().toLowerCase();
            }

            if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

        this.renderTransactions();
        this.updateSortIcons();
    }

    // Update sort icons
    updateSortIcons() {
        document.querySelectorAll('.sortable i').forEach(icon => {
            icon.className = 'fas fa-sort';
        });

        const activeHeader = document.querySelector(`[data-column="${this.sortColumn}"] i`);
        if (activeHeader) {
            activeHeader.className = this.sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
        }
    }

    // Filter transactions
    filterTransactions() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const recurringFilter = document.getElementById('recurringFilter').value;
        const tagFilter = document.getElementById('tagFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;

        let filtered = this.transactions.filter(transaction => {
            const matchesSearch = !searchTerm ||
                transaction.comment.toLowerCase().includes(searchTerm) ||
                transaction.type.toLowerCase().includes(searchTerm) ||
                (transaction.category && transaction.category.toLowerCase().includes(searchTerm)) ||
                transaction.tags.toLowerCase().includes(searchTerm);

            const matchesCategory = !categoryFilter || transaction.category === categoryFilter;
            const matchesRecurring = !recurringFilter || transaction.recurring === recurringFilter;
            const matchesTag = !tagFilter || transaction.tags === tagFilter;
            const matchesDate = !dateFilter || transaction.date === dateFilter;

            return matchesSearch && matchesCategory && matchesRecurring && matchesTag && matchesDate;
        });

        this.renderTransactions(filtered);
    }

    // Render transactions in the grid
    renderTransactions(transactionsToRender = null) {
        const transactions = transactionsToRender || this.transactions;
        const tbody = document.getElementById('transactionTableBody');

        tbody.innerHTML = '';

        transactions.forEach(transaction => {
            const row = document.createElement('tr');
            this.renderSingleRow(row, transaction, this.isEditMode && this.editingRows.has(transaction.id));
            tbody.appendChild(row);
        });

        // If in edit mode, make all rows editable
        if (this.isEditMode) {
            this.enableGridEditing();
        }
    }

    // Update summary statistics
    updateSummaryStats() {
        const totalIncome = this.transactions
            .filter(t => t.type === 'credit')
            .reduce((sum, t) => sum + t.amount, 0);

        const totalExpenses = this.transactions
            .filter(t => t.type === 'debit')
            .reduce((sum, t) => sum + t.amount, 0);

        const netAmount = totalIncome - totalExpenses;

        document.getElementById('totalIncome').textContent = `$${totalIncome.toFixed(2)}`;
        document.getElementById('totalExpenses').textContent = `$${totalExpenses.toFixed(2)}`;
        
        const netElement = document.getElementById('netAmount');
        netElement.textContent = `$${netAmount.toFixed(2)}`;
        netElement.className = `stat-value ${netAmount >= 0 ? 'income' : 'expense'}`;
    }

    // Update summary dashboard
    updateSummaryDashboard() {
        this.updateTagSummary();
        this.updateMonthlySummary();
    }

    // Update tag summary
    updateTagSummary() {
        const tagSummary = {};
        
        this.transactions.forEach(transaction => {
            if (!tagSummary[transaction.tags]) {
                tagSummary[transaction.tags] = { income: 0, expenses: 0, net: 0 };
            }
            
            if (transaction.type === 'credit') {
                tagSummary[transaction.tags].income += transaction.amount;
            } else {
                tagSummary[transaction.tags].expenses += transaction.amount;
            }
            
            tagSummary[transaction.tags].net = tagSummary[transaction.tags].income - tagSummary[transaction.tags].expenses;
        });

        const container = document.getElementById('tagSummary');
        container.innerHTML = '';

        Object.entries(tagSummary).forEach(([tag, data]) => {
            const tagDiv = document.createElement('div');
            tagDiv.style.marginBottom = '1rem';
            tagDiv.innerHTML = `
                <h4>${tag}</h4>
                <p>Income: <span class="income">$${data.income.toFixed(2)}</span></p>
                <p>Expenses: <span class="expense">$${data.expenses.toFixed(2)}</span></p>
                <p>Net: <span class="${data.net >= 0 ? 'income' : 'expense'}">$${data.net.toFixed(2)}</span></p>
                <hr style="margin: 0.5rem 0;">
            `;
            container.appendChild(tagDiv);
        });
    }

    // Update monthly summary
    updateMonthlySummary() {
        const monthlySummary = {};
        
        this.transactions.forEach(transaction => {
            const month = transaction.date.substring(0, 7); // YYYY-MM format
            
            if (!monthlySummary[month]) {
                monthlySummary[month] = { income: 0, expenses: 0, net: 0 };
            }
            
            if (transaction.type === 'credit') {
                monthlySummary[month].income += transaction.amount;
            } else {
                monthlySummary[month].expenses += transaction.amount;
            }
            
            monthlySummary[month].net = monthlySummary[month].income - monthlySummary[month].expenses;
        });

        const container = document.getElementById('monthlySummary');
        container.innerHTML = '';

        // Sort months in descending order
        const sortedMonths = Object.keys(monthlySummary).sort().reverse();

        sortedMonths.forEach(month => {
            const data = monthlySummary[month];
            const monthDiv = document.createElement('div');
            monthDiv.style.marginBottom = '1rem';
            
            const monthName = new Date(month + '-01').toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long' 
            });
            
            monthDiv.innerHTML = `
                <h4>${monthName}</h4>
                <p>Income: <span class="income">$${data.income.toFixed(2)}</span></p>
                <p>Expenses: <span class="expense">$${data.expenses.toFixed(2)}</span></p>
                <p>Net: <span class="${data.net >= 0 ? 'income' : 'expense'}">$${data.net.toFixed(2)}</span></p>
                <hr style="margin: 0.5rem 0;">
            `;
            container.appendChild(monthDiv);
        });
    }

    // Handle keyboard shortcuts
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + N: Add new transaction
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            this.openModal();
        }

        // Escape: Close modal
        if (e.key === 'Escape') {
            this.closeModal();
        }

        // Ctrl/Cmd + E: Export data
        if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
            e.preventDefault();
            this.exportData();
        }
    }

    // Export data to CSV
    exportData() {
        if (this.transactions.length === 0) {
            alert('No transactions to export!');
            return;
        }

        const headers = ['Date', 'Type', 'Amount', 'Comment', 'Tags'];
        const csvContent = [
            headers.join(','),
            ...this.transactions.map(t => [
                t.date,
                t.type,
                t.amount,
                `"${t.comment}"`,
                t.tags
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `finance_transactions_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);

        this.showSuccessMessage('Data exported successfully!');
    }

    // Clear all data (with confirmation)
    clearAllData() {
        if (confirm('Are you sure you want to delete ALL transactions? This action cannot be undone.')) {
            if (confirm('This will permanently delete all your financial data. Are you absolutely sure?')) {
                this.transactions = [];
                this.saveTransactions();
                this.renderTransactions();
                this.updateSummaryStats();
                this.showSuccessMessage('All data cleared successfully!');
            }
        }
    }

    // Toggle edit mode
    toggleEditMode() {
        this.isEditMode = !this.isEditMode;
        const editModeText = document.getElementById('editModeText');
        const editModeControls = document.getElementById('editModeControls');
        const addTransactionBtn = document.getElementById('addTransactionBtn');

        if (this.isEditMode) {
            editModeText.textContent = 'Exit Edit';
            editModeControls.style.display = 'flex';
            addTransactionBtn.style.display = 'none';
            this.enableGridEditing();
        } else {
            editModeText.textContent = 'Edit Grid';
            editModeControls.style.display = 'none';
            addTransactionBtn.style.display = 'inline-flex';
            this.disableGridEditing();
        }
    }

    // Enable grid editing
    enableGridEditing() {
        const rows = document.querySelectorAll('#transactionTableBody tr');
        rows.forEach((row, index) => {
            this.makeRowEditable(row, this.transactions[index]);
        });
    }

    // Disable grid editing
    disableGridEditing() {
        this.editingRows.clear();
        this.originalData.clear();
        this.renderTransactions();
    }

    // Make a row editable
    makeRowEditable(row, transaction) {
        if (!transaction) return;

        // Store original data
        this.originalData.set(transaction.id, { ...transaction });
        this.editingRows.add(transaction.id);

        row.classList.add('edit-row');
        const cells = row.children;

        // Date cell
        cells[0].innerHTML = `<input type="date" class="edit-input" value="${transaction.date}" data-field="date">`;

        // Type cell
        cells[1].innerHTML = `
            <select class="edit-select" data-field="type">
                <option value="credit" ${transaction.type === 'credit' ? 'selected' : ''}>Credit</option>
                <option value="debit" ${transaction.type === 'debit' ? 'selected' : ''}>Debit</option>
            </select>
        `;

        // Amount cell
        cells[2].innerHTML = `<input type="number" class="edit-input" value="${transaction.amount}" step="0.01" min="0" data-field="amount">`;

        // Category cell
        cells[3].innerHTML = `
            <select class="edit-select" data-field="category">
                ${this.categories.map(cat =>
                    `<option value="${cat.name}" ${transaction.category === cat.name ? 'selected' : ''}>${cat.name}</option>`
                ).join('')}
            </select>
        `;

        // Recurring cell
        cells[4].innerHTML = `
            <select class="edit-select" data-field="recurring">
                <option value="Yes" ${transaction.recurring === 'Yes' ? 'selected' : ''}>Yes</option>
                <option value="No" ${transaction.recurring === 'No' ? 'selected' : ''}>No</option>
            </select>
        `;

        // Comment cell
        cells[5].innerHTML = `<input type="text" class="edit-input" value="${transaction.comment}" data-field="comment">`;

        // Tags cell
        cells[6].innerHTML = `
            <select class="edit-select" data-field="tags">
                ${this.tags.map(tag =>
                    `<option value="${tag.name}" ${transaction.tags === tag.name ? 'selected' : ''}>${tag.name}</option>`
                ).join('')}
            </select>
        `;

        // Actions cell - add row-specific save/cancel buttons
        const isNewRow = transaction.id.startsWith('new_');
        cells[7].innerHTML = `
            <div class="action-buttons">
                <button class="action-btn btn-success" onclick="dashboard.${isNewRow ? 'saveNewRow' : 'saveRow'}('${transaction.id}')" title="Save row">
                    <i class="fas fa-check"></i>
                </button>
                <button class="action-btn btn-warning" onclick="dashboard.${isNewRow ? 'cancelNewRow' : 'cancelRow'}('${transaction.id}')" title="Cancel changes">
                    <i class="fas fa-times"></i>
                </button>
                ${!isNewRow ? `
                    <button class="action-btn delete-btn" onclick="dashboard.deleteTransaction('${transaction.id}')" title="Delete row">
                        <i class="fas fa-trash"></i>
                    </button>
                ` : ''}
            </div>
        `;
    }

    // Save individual row
    saveRow(transactionId) {
        const row = document.querySelector(`[onclick*="${transactionId}"]`).closest('tr');
        const inputs = row.querySelectorAll('.edit-input, .edit-select');
        const updatedData = {};

        inputs.forEach(input => {
            const field = input.dataset.field;
            let value = input.value;

            if (field === 'amount') {
                value = parseFloat(value);
                if (isNaN(value) || value <= 0) {
                    alert('Please enter a valid amount');
                    return;
                }
            }

            updatedData[field] = value;
        });

        // Validate required fields
        if (!updatedData.date || !updatedData.type || !updatedData.amount || !updatedData.category || !updatedData.recurring || !updatedData.tags) {
            alert('Please fill in all required fields');
            return;
        }

        // Update transaction
        const transactionIndex = this.transactions.findIndex(t => t.id === transactionId);
        if (transactionIndex !== -1) {
            this.transactions[transactionIndex] = { ...this.transactions[transactionIndex], ...updatedData };
            this.saveTransactions();
            this.updateSummaryStats();

            // Remove from editing state
            this.editingRows.delete(transactionId);
            this.originalData.delete(transactionId);

            // Re-render the specific row
            this.renderSingleRow(row, this.transactions[transactionIndex], false);
            this.showSuccessMessage('Row saved successfully!');
        }
    }

    // Cancel individual row changes
    cancelRow(transactionId) {
        const row = document.querySelector(`[onclick*="${transactionId}"]`).closest('tr');
        const originalData = this.originalData.get(transactionId);

        if (originalData) {
            // Remove from editing state
            this.editingRows.delete(transactionId);
            this.originalData.delete(transactionId);

            // Re-render the row with original data
            this.renderSingleRow(row, originalData, false);
        }
    }

    // Render a single row
    renderSingleRow(row, transaction, editable = false) {
        const formattedDate = new Date(transaction.date).toLocaleDateString();
        const formattedAmount = `$${transaction.amount.toFixed(2)}`;

        row.className = editable ? 'edit-row' : '';

        // Get category and tag colors
        const categoryObj = this.categories.find(cat => cat.name === transaction.category);
        const tagObj = this.tags.find(tag => tag.name === transaction.tags);

        row.innerHTML = `
            <td>${formattedDate}</td>
            <td><span class="type-${transaction.type}">${transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}</span></td>
            <td><span class="amount-${transaction.type}">${formattedAmount}</span></td>
            <td>
                <span class="category-item" style="background-color: ${categoryObj?.color || '#667eea'}">
                    ${transaction.category || 'N/A'}
                </span>
            </td>
            <td>
                <span class="recurring-${transaction.recurring?.toLowerCase() || 'no'}">${transaction.recurring || 'No'}</span>
            </td>
            <td>${transaction.comment}</td>
            <td>
                <span class="tag-item" style="background-color: ${tagObj?.color || '#28a745'}">
                    ${transaction.tags}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    ${this.isEditMode ? `
                        <button class="action-btn edit-btn" onclick="dashboard.makeRowEditable(this.closest('tr'), ${JSON.stringify(transaction).replace(/"/g, '&quot;')})">
                            <i class="fas fa-edit"></i>
                        </button>
                    ` : `
                        <button class="action-btn edit-btn" onclick="dashboard.openModal(${JSON.stringify(transaction).replace(/"/g, '&quot;')})">
                            <i class="fas fa-edit"></i>
                        </button>
                    `}
                    <button class="action-btn delete-btn" onclick="dashboard.deleteTransaction('${transaction.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        if (editable) {
            this.makeRowEditable(row, transaction);
        }
    }

    // Save all changes in edit mode
    saveAllChanges() {
        const editRows = document.querySelectorAll('.edit-row');
        let hasErrors = false;
        const updates = [];

        editRows.forEach(row => {
            const inputs = row.querySelectorAll('.edit-input, .edit-select');
            const updatedData = {};
            let transactionId = null;

            inputs.forEach(input => {
                const field = input.dataset.field;
                let value = input.value;

                if (field === 'amount') {
                    value = parseFloat(value);
                    if (isNaN(value) || value <= 0) {
                        hasErrors = true;
                        input.style.borderColor = 'var(--danger)';
                        return;
                    }
                }

                updatedData[field] = value;
            });

            // Find transaction ID from the row's action buttons
            const actionButtons = row.querySelector('.action-buttons');
            if (actionButtons) {
                const saveButton = actionButtons.querySelector('[onclick*="saveRow"]');
                if (saveButton) {
                    const match = saveButton.getAttribute('onclick').match(/'([^']+)'/);
                    if (match) {
                        transactionId = match[1];
                    }
                }
            }

            // Validate required fields
            if (!updatedData.date || !updatedData.type || !updatedData.amount || !updatedData.tags) {
                hasErrors = true;
                return;
            }

            if (transactionId) {
                updates.push({ id: transactionId, data: updatedData });
            }
        });

        if (hasErrors) {
            alert('Please fix the highlighted errors before saving');
            return;
        }

        // Apply all updates
        updates.forEach(update => {
            const transactionIndex = this.transactions.findIndex(t => t.id === update.id);
            if (transactionIndex !== -1) {
                this.transactions[transactionIndex] = { ...this.transactions[transactionIndex], ...update.data };
            }
        });

        this.saveTransactions();
        this.updateSummaryStats();
        this.editingRows.clear();
        this.originalData.clear();
        this.renderTransactions();
        this.showSuccessMessage(`${updates.length} transactions saved successfully!`);
    }

    // Cancel all changes in edit mode
    cancelAllChanges() {
        if (this.editingRows.size > 0) {
            if (confirm('Are you sure you want to cancel all changes? Any unsaved modifications will be lost.')) {
                this.editingRows.clear();
                this.originalData.clear();
                this.renderTransactions();
                this.showSuccessMessage('All changes cancelled');
            }
        } else {
            this.toggleEditMode();
        }
    }

    // Add new row in edit mode
    addNewRow() {
        const newTransaction = {
            id: 'new_' + Date.now(),
            date: new Date().toISOString().split('T')[0],
            type: 'debit',
            amount: 0,
            category: this.categories.length > 0 ? this.categories[0].name : '',
            recurring: 'No',
            comment: '',
            tags: this.tags.length > 0 ? this.tags[0].name : ''
        };

        // Add to transactions temporarily
        this.transactions.unshift(newTransaction);

        // Re-render and make the new row editable
        this.renderTransactions();

        // Find the new row and make it editable
        const tbody = document.getElementById('transactionTableBody');
        const firstRow = tbody.firstElementChild;
        if (firstRow) {
            this.makeRowEditable(firstRow, newTransaction);

            // Focus on the first input
            const firstInput = firstRow.querySelector('.edit-input');
            if (firstInput) {
                firstInput.focus();
            }
        }
    }

    // Handle new row save (different from regular save)
    saveNewRow(tempId) {
        const row = document.querySelector(`[onclick*="${tempId}"]`).closest('tr');
        const inputs = row.querySelectorAll('.edit-input, .edit-select');
        const newData = {};

        inputs.forEach(input => {
            const field = input.dataset.field;
            let value = input.value;

            if (field === 'amount') {
                value = parseFloat(value);
                if (isNaN(value) || value <= 0) {
                    alert('Please enter a valid amount');
                    return;
                }
            }

            newData[field] = value;
        });

        // Validate required fields
        if (!newData.date || !newData.type || !newData.amount || !newData.category || !newData.recurring || !newData.tags) {
            alert('Please fill in all required fields');
            return;
        }

        // Replace temporary transaction with real one
        const tempIndex = this.transactions.findIndex(t => t.id === tempId);
        if (tempIndex !== -1) {
            const realTransaction = {
                ...newData,
                id: Date.now().toString()
            };

            this.transactions[tempIndex] = realTransaction;
            this.saveTransactions();
            this.updateSummaryStats();

            // Re-render the row
            this.renderSingleRow(row, realTransaction, false);
            this.showSuccessMessage('New transaction added successfully!');
        }
    }

    // Cancel new row
    cancelNewRow(tempId) {
        // Remove the temporary transaction
        this.transactions = this.transactions.filter(t => t.id !== tempId);
        this.renderTransactions();
    }
}

// Initialize the dashboard when the page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new FinanceDashboard();
});
