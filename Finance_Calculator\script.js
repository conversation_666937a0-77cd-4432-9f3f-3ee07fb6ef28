// Personal Finance Dashboard JavaScript

class FinanceDashboard {
    constructor() {
        this.transactions = this.loadTransactions();
        this.currentEditId = null;
        this.sortColumn = 'date';
        this.sortDirection = 'desc';
        this.isEditMode = false;
        this.editingRows = new Set();
        this.originalData = new Map();

        this.initializeTheme();
        this.initializeEventListeners();
        this.renderTransactions();
        this.updateSummaryStats();
        this.setupTabNavigation();
    }

    // Initialize theme
    initializeTheme() {
        const savedTheme = localStorage.getItem('financeTheme') || 'light';
        this.setTheme(savedTheme);
    }

    // Set theme
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        const themeIcon = document.getElementById('themeIcon');
        if (themeIcon) {
            themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }
        localStorage.setItem('financeTheme', theme);
    }

    // Toggle theme
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }

    // Initialize all event listeners
    initializeEventListeners() {
        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());

        // Modal controls
        document.getElementById('addTransactionBtn').addEventListener('click', () => this.openModal());
        document.getElementById('closeModal').addEventListener('click', () => this.closeModal());
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeModal());

        // Form submission
        document.getElementById('transactionForm').addEventListener('submit', (e) => this.handleFormSubmit(e));

        // Grid edit mode controls
        document.getElementById('toggleEditMode').addEventListener('click', () => this.toggleEditMode());
        document.getElementById('saveAllBtn').addEventListener('click', () => this.saveAllChanges());
        document.getElementById('addRowBtn').addEventListener('click', () => this.addNewRow());
        document.getElementById('cancelAllBtn').addEventListener('click', () => this.cancelAllChanges());

        // Search and filters
        document.getElementById('searchInput').addEventListener('input', () => this.filterTransactions());
        document.getElementById('tagFilter').addEventListener('change', () => this.filterTransactions());
        document.getElementById('dateFilter').addEventListener('change', () => this.filterTransactions());

        // Export functionality
        document.getElementById('exportBtn').addEventListener('click', () => this.exportData());

        // Clear data functionality
        document.getElementById('clearDataBtn').addEventListener('click', () => this.clearAllData());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        // Close modal when clicking outside
        document.getElementById('transactionModal').addEventListener('click', (e) => {
            if (e.target.id === 'transactionModal') {
                this.closeModal();
            }
        });

        // Column sorting
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => {
                const column = header.dataset.column;
                this.sortTransactions(column);
            });
        });
    }

    // Setup tab navigation
    setupTabNavigation() {
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                this.switchTab(tabName);
            });
        });
    }

    // Switch between tabs
    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        // Update summary if switching to summary tab
        if (tabName === 'summary') {
            this.updateSummaryDashboard();
        }
    }

    // Load transactions from localStorage
    loadTransactions() {
        const stored = localStorage.getItem('financeTransactions');
        if (stored) {
            return JSON.parse(stored);
        } else {
            // Return sample data for first-time users
            return this.getSampleData();
        }
    }

    // Get sample data for demonstration
    getSampleData() {
        const today = new Date();
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        const twoWeeksAgo = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 14);

        return [
            {
                id: '1',
                date: today.toISOString().split('T')[0],
                type: 'credit',
                amount: 3500.00,
                comment: 'Salary deposit',
                tags: 'Both'
            },
            {
                id: '2',
                date: twoWeeksAgo.toISOString().split('T')[0],
                type: 'debit',
                amount: 1200.00,
                comment: 'Rent payment',
                tags: 'Both'
            },
            {
                id: '3',
                date: lastMonth.toISOString().split('T')[0],
                type: 'debit',
                amount: 85.50,
                comment: 'Grocery shopping',
                tags: 'Nikita'
            },
            {
                id: '4',
                date: lastMonth.toISOString().split('T')[0],
                type: 'debit',
                amount: 45.00,
                comment: 'Gas station',
                tags: 'Lance'
            },
            {
                id: '5',
                date: new Date(today.getFullYear(), today.getMonth(), today.getDate() - 3).toISOString().split('T')[0],
                type: 'credit',
                amount: 250.00,
                comment: 'Freelance work',
                tags: 'Lance'
            }
        ];
    }

    // Save transactions to localStorage
    saveTransactions() {
        localStorage.setItem('financeTransactions', JSON.stringify(this.transactions));
    }

    // Open modal for adding/editing transaction
    openModal(transaction = null) {
        const modal = document.getElementById('transactionModal');
        const form = document.getElementById('transactionForm');
        const title = document.getElementById('modalTitle');

        if (transaction) {
            // Edit mode
            title.textContent = 'Edit Transaction';
            this.currentEditId = transaction.id;
            
            document.getElementById('transactionDate').value = transaction.date;
            document.getElementById('transactionType').value = transaction.type;
            document.getElementById('transactionAmount').value = transaction.amount;
            document.getElementById('transactionComment').value = transaction.comment;
            document.getElementById('transactionTags').value = transaction.tags;
        } else {
            // Add mode
            title.textContent = 'Add Transaction';
            this.currentEditId = null;
            form.reset();
            
            // Set today's date as default
            document.getElementById('transactionDate').value = new Date().toISOString().split('T')[0];
        }

        modal.style.display = 'block';
    }

    // Close modal
    closeModal() {
        document.getElementById('transactionModal').style.display = 'none';
        this.currentEditId = null;
    }

    // Handle form submission
    handleFormSubmit(e) {
        e.preventDefault();

        // Validate form data
        const validation = this.validateFormData();
        if (!validation.isValid) {
            this.showValidationErrors(validation.errors);
            return;
        }

        const formData = {
            date: document.getElementById('transactionDate').value,
            type: document.getElementById('transactionType').value,
            amount: parseFloat(document.getElementById('transactionAmount').value),
            comment: document.getElementById('transactionComment').value.trim(),
            tags: document.getElementById('transactionTags').value
        };

        if (this.currentEditId) {
            // Update existing transaction
            const index = this.transactions.findIndex(t => t.id === this.currentEditId);
            if (index !== -1) {
                this.transactions[index] = { ...formData, id: this.currentEditId };
            }
        } else {
            // Add new transaction
            const newTransaction = {
                ...formData,
                id: Date.now().toString()
            };
            this.transactions.push(newTransaction);
        }

        this.saveTransactions();
        this.renderTransactions();
        this.updateSummaryStats();
        this.closeModal();
        this.showSuccessMessage(this.currentEditId ? 'Transaction updated successfully!' : 'Transaction added successfully!');
    }

    // Validate form data
    validateFormData() {
        const errors = [];

        const date = document.getElementById('transactionDate').value;
        const type = document.getElementById('transactionType').value;
        const amount = document.getElementById('transactionAmount').value;
        const tags = document.getElementById('transactionTags').value;

        // Date validation
        if (!date) {
            errors.push('Date is required');
        } else {
            const selectedDate = new Date(date);
            const today = new Date();
            const oneYearFromNow = new Date();
            oneYearFromNow.setFullYear(today.getFullYear() + 1);

            if (selectedDate > oneYearFromNow) {
                errors.push('Date cannot be more than one year in the future');
            }
        }

        // Type validation
        if (!type) {
            errors.push('Transaction type is required');
        }

        // Amount validation
        if (!amount) {
            errors.push('Amount is required');
        } else {
            const numAmount = parseFloat(amount);
            if (isNaN(numAmount) || numAmount <= 0) {
                errors.push('Amount must be a positive number');
            } else if (numAmount > 1000000) {
                errors.push('Amount cannot exceed $1,000,000');
            }
        }

        // Tags validation
        if (!tags) {
            errors.push('Tag is required');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // Show validation errors
    showValidationErrors(errors) {
        this.removeExistingMessages();

        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-errors';
        errorDiv.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Please fix the following errors:</strong>
                <ul>
                    ${errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
            </div>
        `;

        const form = document.getElementById('transactionForm');
        form.insertBefore(errorDiv, form.firstChild);
    }

    // Show success message
    showSuccessMessage(message) {
        this.removeExistingMessages();

        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.innerHTML = `
            <div class="success-content">
                <i class="fas fa-check-circle"></i>
                ${message}
            </div>
        `;

        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(successDiv, mainContent.firstChild);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    }

    // Remove existing messages
    removeExistingMessages() {
        const existingErrors = document.querySelector('.validation-errors');
        const existingSuccess = document.querySelector('.success-message');

        if (existingErrors) {
            existingErrors.parentNode.removeChild(existingErrors);
        }
        if (existingSuccess) {
            existingSuccess.parentNode.removeChild(existingSuccess);
        }
    }

    // Delete transaction
    deleteTransaction(id) {
        if (confirm('Are you sure you want to delete this transaction?')) {
            this.transactions = this.transactions.filter(t => t.id !== id);
            this.saveTransactions();
            this.renderTransactions();
            this.updateSummaryStats();
        }
    }

    // Sort transactions
    sortTransactions(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }

        this.transactions.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            // Handle different data types
            if (column === 'date') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            } else if (column === 'amount') {
                aVal = parseFloat(aVal);
                bVal = parseFloat(bVal);
            } else {
                aVal = aVal.toString().toLowerCase();
                bVal = bVal.toString().toLowerCase();
            }

            if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

        this.renderTransactions();
        this.updateSortIcons();
    }

    // Update sort icons
    updateSortIcons() {
        document.querySelectorAll('.sortable i').forEach(icon => {
            icon.className = 'fas fa-sort';
        });

        const activeHeader = document.querySelector(`[data-column="${this.sortColumn}"] i`);
        if (activeHeader) {
            activeHeader.className = this.sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
        }
    }

    // Filter transactions
    filterTransactions() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const tagFilter = document.getElementById('tagFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;

        let filtered = this.transactions.filter(transaction => {
            const matchesSearch = !searchTerm || 
                transaction.comment.toLowerCase().includes(searchTerm) ||
                transaction.type.toLowerCase().includes(searchTerm) ||
                transaction.tags.toLowerCase().includes(searchTerm);

            const matchesTag = !tagFilter || transaction.tags === tagFilter;
            const matchesDate = !dateFilter || transaction.date === dateFilter;

            return matchesSearch && matchesTag && matchesDate;
        });

        this.renderTransactions(filtered);
    }

    // Render transactions in the grid
    renderTransactions(transactionsToRender = null) {
        const transactions = transactionsToRender || this.transactions;
        const tbody = document.getElementById('transactionTableBody');

        tbody.innerHTML = '';

        transactions.forEach(transaction => {
            const row = document.createElement('tr');
            this.renderSingleRow(row, transaction, this.isEditMode && this.editingRows.has(transaction.id));
            tbody.appendChild(row);
        });

        // If in edit mode, make all rows editable
        if (this.isEditMode) {
            this.enableGridEditing();
        }
    }

    // Update summary statistics
    updateSummaryStats() {
        const totalIncome = this.transactions
            .filter(t => t.type === 'credit')
            .reduce((sum, t) => sum + t.amount, 0);

        const totalExpenses = this.transactions
            .filter(t => t.type === 'debit')
            .reduce((sum, t) => sum + t.amount, 0);

        const netAmount = totalIncome - totalExpenses;

        document.getElementById('totalIncome').textContent = `$${totalIncome.toFixed(2)}`;
        document.getElementById('totalExpenses').textContent = `$${totalExpenses.toFixed(2)}`;
        
        const netElement = document.getElementById('netAmount');
        netElement.textContent = `$${netAmount.toFixed(2)}`;
        netElement.className = `stat-value ${netAmount >= 0 ? 'income' : 'expense'}`;
    }

    // Update summary dashboard
    updateSummaryDashboard() {
        this.updateTagSummary();
        this.updateMonthlySummary();
    }

    // Update tag summary
    updateTagSummary() {
        const tagSummary = {};
        
        this.transactions.forEach(transaction => {
            if (!tagSummary[transaction.tags]) {
                tagSummary[transaction.tags] = { income: 0, expenses: 0, net: 0 };
            }
            
            if (transaction.type === 'credit') {
                tagSummary[transaction.tags].income += transaction.amount;
            } else {
                tagSummary[transaction.tags].expenses += transaction.amount;
            }
            
            tagSummary[transaction.tags].net = tagSummary[transaction.tags].income - tagSummary[transaction.tags].expenses;
        });

        const container = document.getElementById('tagSummary');
        container.innerHTML = '';

        Object.entries(tagSummary).forEach(([tag, data]) => {
            const tagDiv = document.createElement('div');
            tagDiv.style.marginBottom = '1rem';
            tagDiv.innerHTML = `
                <h4>${tag}</h4>
                <p>Income: <span class="income">$${data.income.toFixed(2)}</span></p>
                <p>Expenses: <span class="expense">$${data.expenses.toFixed(2)}</span></p>
                <p>Net: <span class="${data.net >= 0 ? 'income' : 'expense'}">$${data.net.toFixed(2)}</span></p>
                <hr style="margin: 0.5rem 0;">
            `;
            container.appendChild(tagDiv);
        });
    }

    // Update monthly summary
    updateMonthlySummary() {
        const monthlySummary = {};
        
        this.transactions.forEach(transaction => {
            const month = transaction.date.substring(0, 7); // YYYY-MM format
            
            if (!monthlySummary[month]) {
                monthlySummary[month] = { income: 0, expenses: 0, net: 0 };
            }
            
            if (transaction.type === 'credit') {
                monthlySummary[month].income += transaction.amount;
            } else {
                monthlySummary[month].expenses += transaction.amount;
            }
            
            monthlySummary[month].net = monthlySummary[month].income - monthlySummary[month].expenses;
        });

        const container = document.getElementById('monthlySummary');
        container.innerHTML = '';

        // Sort months in descending order
        const sortedMonths = Object.keys(monthlySummary).sort().reverse();

        sortedMonths.forEach(month => {
            const data = monthlySummary[month];
            const monthDiv = document.createElement('div');
            monthDiv.style.marginBottom = '1rem';
            
            const monthName = new Date(month + '-01').toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long' 
            });
            
            monthDiv.innerHTML = `
                <h4>${monthName}</h4>
                <p>Income: <span class="income">$${data.income.toFixed(2)}</span></p>
                <p>Expenses: <span class="expense">$${data.expenses.toFixed(2)}</span></p>
                <p>Net: <span class="${data.net >= 0 ? 'income' : 'expense'}">$${data.net.toFixed(2)}</span></p>
                <hr style="margin: 0.5rem 0;">
            `;
            container.appendChild(monthDiv);
        });
    }

    // Handle keyboard shortcuts
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + N: Add new transaction
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            this.openModal();
        }

        // Escape: Close modal
        if (e.key === 'Escape') {
            this.closeModal();
        }

        // Ctrl/Cmd + E: Export data
        if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
            e.preventDefault();
            this.exportData();
        }
    }

    // Export data to CSV
    exportData() {
        if (this.transactions.length === 0) {
            alert('No transactions to export!');
            return;
        }

        const headers = ['Date', 'Type', 'Amount', 'Comment', 'Tags'];
        const csvContent = [
            headers.join(','),
            ...this.transactions.map(t => [
                t.date,
                t.type,
                t.amount,
                `"${t.comment}"`,
                t.tags
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `finance_transactions_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);

        this.showSuccessMessage('Data exported successfully!');
    }

    // Clear all data (with confirmation)
    clearAllData() {
        if (confirm('Are you sure you want to delete ALL transactions? This action cannot be undone.')) {
            if (confirm('This will permanently delete all your financial data. Are you absolutely sure?')) {
                this.transactions = [];
                this.saveTransactions();
                this.renderTransactions();
                this.updateSummaryStats();
                this.showSuccessMessage('All data cleared successfully!');
            }
        }
    }

    // Toggle edit mode
    toggleEditMode() {
        this.isEditMode = !this.isEditMode;
        const editModeText = document.getElementById('editModeText');
        const editModeControls = document.getElementById('editModeControls');
        const addTransactionBtn = document.getElementById('addTransactionBtn');

        if (this.isEditMode) {
            editModeText.textContent = 'Exit Edit';
            editModeControls.style.display = 'flex';
            addTransactionBtn.style.display = 'none';
            this.enableGridEditing();
        } else {
            editModeText.textContent = 'Edit Grid';
            editModeControls.style.display = 'none';
            addTransactionBtn.style.display = 'inline-flex';
            this.disableGridEditing();
        }
    }

    // Enable grid editing
    enableGridEditing() {
        const rows = document.querySelectorAll('#transactionTableBody tr');
        rows.forEach((row, index) => {
            this.makeRowEditable(row, this.transactions[index]);
        });
    }

    // Disable grid editing
    disableGridEditing() {
        this.editingRows.clear();
        this.originalData.clear();
        this.renderTransactions();
    }

    // Make a row editable
    makeRowEditable(row, transaction) {
        if (!transaction) return;

        // Store original data
        this.originalData.set(transaction.id, { ...transaction });
        this.editingRows.add(transaction.id);

        row.classList.add('edit-row');
        const cells = row.children;

        // Date cell
        cells[0].innerHTML = `<input type="date" class="edit-input" value="${transaction.date}" data-field="date">`;

        // Type cell
        cells[1].innerHTML = `
            <select class="edit-select" data-field="type">
                <option value="credit" ${transaction.type === 'credit' ? 'selected' : ''}>Credit</option>
                <option value="debit" ${transaction.type === 'debit' ? 'selected' : ''}>Debit</option>
            </select>
        `;

        // Amount cell
        cells[2].innerHTML = `<input type="number" class="edit-input" value="${transaction.amount}" step="0.01" min="0" data-field="amount">`;

        // Comment cell
        cells[3].innerHTML = `<input type="text" class="edit-input" value="${transaction.comment}" data-field="comment">`;

        // Tags cell
        cells[4].innerHTML = `
            <select class="edit-select" data-field="tags">
                <option value="Nikita" ${transaction.tags === 'Nikita' ? 'selected' : ''}>Nikita</option>
                <option value="Lance" ${transaction.tags === 'Lance' ? 'selected' : ''}>Lance</option>
                <option value="Both" ${transaction.tags === 'Both' ? 'selected' : ''}>Both</option>
            </select>
        `;

        // Actions cell - add row-specific save/cancel buttons
        const isNewRow = transaction.id.startsWith('new_');
        cells[5].innerHTML = `
            <div class="action-buttons">
                <button class="action-btn btn-success" onclick="dashboard.${isNewRow ? 'saveNewRow' : 'saveRow'}('${transaction.id}')" title="Save row">
                    <i class="fas fa-check"></i>
                </button>
                <button class="action-btn btn-warning" onclick="dashboard.${isNewRow ? 'cancelNewRow' : 'cancelRow'}('${transaction.id}')" title="Cancel changes">
                    <i class="fas fa-times"></i>
                </button>
                ${!isNewRow ? `
                    <button class="action-btn delete-btn" onclick="dashboard.deleteTransaction('${transaction.id}')" title="Delete row">
                        <i class="fas fa-trash"></i>
                    </button>
                ` : ''}
            </div>
        `;
    }

    // Save individual row
    saveRow(transactionId) {
        const row = document.querySelector(`[onclick*="${transactionId}"]`).closest('tr');
        const inputs = row.querySelectorAll('.edit-input, .edit-select');
        const updatedData = {};

        inputs.forEach(input => {
            const field = input.dataset.field;
            let value = input.value;

            if (field === 'amount') {
                value = parseFloat(value);
                if (isNaN(value) || value <= 0) {
                    alert('Please enter a valid amount');
                    return;
                }
            }

            updatedData[field] = value;
        });

        // Validate required fields
        if (!updatedData.date || !updatedData.type || !updatedData.amount || !updatedData.tags) {
            alert('Please fill in all required fields');
            return;
        }

        // Update transaction
        const transactionIndex = this.transactions.findIndex(t => t.id === transactionId);
        if (transactionIndex !== -1) {
            this.transactions[transactionIndex] = { ...this.transactions[transactionIndex], ...updatedData };
            this.saveTransactions();
            this.updateSummaryStats();

            // Remove from editing state
            this.editingRows.delete(transactionId);
            this.originalData.delete(transactionId);

            // Re-render the specific row
            this.renderSingleRow(row, this.transactions[transactionIndex], false);
            this.showSuccessMessage('Row saved successfully!');
        }
    }

    // Cancel individual row changes
    cancelRow(transactionId) {
        const row = document.querySelector(`[onclick*="${transactionId}"]`).closest('tr');
        const originalData = this.originalData.get(transactionId);

        if (originalData) {
            // Remove from editing state
            this.editingRows.delete(transactionId);
            this.originalData.delete(transactionId);

            // Re-render the row with original data
            this.renderSingleRow(row, originalData, false);
        }
    }

    // Render a single row
    renderSingleRow(row, transaction, editable = false) {
        const formattedDate = new Date(transaction.date).toLocaleDateString();
        const formattedAmount = `$${transaction.amount.toFixed(2)}`;

        row.className = editable ? 'edit-row' : '';
        row.innerHTML = `
            <td>${formattedDate}</td>
            <td><span class="type-${transaction.type}">${transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}</span></td>
            <td><span class="amount-${transaction.type}">${formattedAmount}</span></td>
            <td>${transaction.comment}</td>
            <td><span class="tag tag-${transaction.tags.toLowerCase()}">${transaction.tags}</span></td>
            <td>
                <div class="action-buttons">
                    ${this.isEditMode ? `
                        <button class="action-btn edit-btn" onclick="dashboard.makeRowEditable(this.closest('tr'), ${JSON.stringify(transaction).replace(/"/g, '&quot;')})">
                            <i class="fas fa-edit"></i>
                        </button>
                    ` : `
                        <button class="action-btn edit-btn" onclick="dashboard.openModal(${JSON.stringify(transaction).replace(/"/g, '&quot;')})">
                            <i class="fas fa-edit"></i>
                        </button>
                    `}
                    <button class="action-btn delete-btn" onclick="dashboard.deleteTransaction('${transaction.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        if (editable) {
            this.makeRowEditable(row, transaction);
        }
    }

    // Save all changes in edit mode
    saveAllChanges() {
        const editRows = document.querySelectorAll('.edit-row');
        let hasErrors = false;
        const updates = [];

        editRows.forEach(row => {
            const inputs = row.querySelectorAll('.edit-input, .edit-select');
            const updatedData = {};
            let transactionId = null;

            inputs.forEach(input => {
                const field = input.dataset.field;
                let value = input.value;

                if (field === 'amount') {
                    value = parseFloat(value);
                    if (isNaN(value) || value <= 0) {
                        hasErrors = true;
                        input.style.borderColor = 'var(--danger)';
                        return;
                    }
                }

                updatedData[field] = value;
            });

            // Find transaction ID from the row's action buttons
            const actionButtons = row.querySelector('.action-buttons');
            if (actionButtons) {
                const saveButton = actionButtons.querySelector('[onclick*="saveRow"]');
                if (saveButton) {
                    const match = saveButton.getAttribute('onclick').match(/'([^']+)'/);
                    if (match) {
                        transactionId = match[1];
                    }
                }
            }

            // Validate required fields
            if (!updatedData.date || !updatedData.type || !updatedData.amount || !updatedData.tags) {
                hasErrors = true;
                return;
            }

            if (transactionId) {
                updates.push({ id: transactionId, data: updatedData });
            }
        });

        if (hasErrors) {
            alert('Please fix the highlighted errors before saving');
            return;
        }

        // Apply all updates
        updates.forEach(update => {
            const transactionIndex = this.transactions.findIndex(t => t.id === update.id);
            if (transactionIndex !== -1) {
                this.transactions[transactionIndex] = { ...this.transactions[transactionIndex], ...update.data };
            }
        });

        this.saveTransactions();
        this.updateSummaryStats();
        this.editingRows.clear();
        this.originalData.clear();
        this.renderTransactions();
        this.showSuccessMessage(`${updates.length} transactions saved successfully!`);
    }

    // Cancel all changes in edit mode
    cancelAllChanges() {
        if (this.editingRows.size > 0) {
            if (confirm('Are you sure you want to cancel all changes? Any unsaved modifications will be lost.')) {
                this.editingRows.clear();
                this.originalData.clear();
                this.renderTransactions();
                this.showSuccessMessage('All changes cancelled');
            }
        } else {
            this.toggleEditMode();
        }
    }

    // Add new row in edit mode
    addNewRow() {
        const newTransaction = {
            id: 'new_' + Date.now(),
            date: new Date().toISOString().split('T')[0],
            type: 'debit',
            amount: 0,
            comment: '',
            tags: 'Both'
        };

        // Add to transactions temporarily
        this.transactions.unshift(newTransaction);

        // Re-render and make the new row editable
        this.renderTransactions();

        // Find the new row and make it editable
        const tbody = document.getElementById('transactionTableBody');
        const firstRow = tbody.firstElementChild;
        if (firstRow) {
            this.makeRowEditable(firstRow, newTransaction);

            // Focus on the first input
            const firstInput = firstRow.querySelector('.edit-input');
            if (firstInput) {
                firstInput.focus();
            }
        }
    }

    // Handle new row save (different from regular save)
    saveNewRow(tempId) {
        const row = document.querySelector(`[onclick*="${tempId}"]`).closest('tr');
        const inputs = row.querySelectorAll('.edit-input, .edit-select');
        const newData = {};

        inputs.forEach(input => {
            const field = input.dataset.field;
            let value = input.value;

            if (field === 'amount') {
                value = parseFloat(value);
                if (isNaN(value) || value <= 0) {
                    alert('Please enter a valid amount');
                    return;
                }
            }

            newData[field] = value;
        });

        // Validate required fields
        if (!newData.date || !newData.type || !newData.amount || !newData.tags) {
            alert('Please fill in all required fields');
            return;
        }

        // Replace temporary transaction with real one
        const tempIndex = this.transactions.findIndex(t => t.id === tempId);
        if (tempIndex !== -1) {
            const realTransaction = {
                ...newData,
                id: Date.now().toString()
            };

            this.transactions[tempIndex] = realTransaction;
            this.saveTransactions();
            this.updateSummaryStats();

            // Re-render the row
            this.renderSingleRow(row, realTransaction, false);
            this.showSuccessMessage('New transaction added successfully!');
        }
    }

    // Cancel new row
    cancelNewRow(tempId) {
        // Remove the temporary transaction
        this.transactions = this.transactions.filter(t => t.id !== tempId);
        this.renderTransactions();
    }
}

// Initialize the dashboard when the page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new FinanceDashboard();
});
