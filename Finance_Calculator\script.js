// Personal Finance Dashboard JavaScript

class FinanceDashboard {
    constructor() {
        this.transactions = this.loadTransactions();
        this.currentEditId = null;
        this.sortColumn = 'date';
        this.sortDirection = 'desc';
        
        this.initializeEventListeners();
        this.renderTransactions();
        this.updateSummaryStats();
        this.setupTabNavigation();
    }

    // Initialize all event listeners
    initializeEventListeners() {
        // Modal controls
        document.getElementById('addTransactionBtn').addEventListener('click', () => this.openModal());
        document.getElementById('closeModal').addEventListener('click', () => this.closeModal());
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeModal());
        
        // Form submission
        document.getElementById('transactionForm').addEventListener('submit', (e) => this.handleFormSubmit(e));
        
        // Search and filters
        document.getElementById('searchInput').addEventListener('input', () => this.filterTransactions());
        document.getElementById('tagFilter').addEventListener('change', () => this.filterTransactions());
        document.getElementById('dateFilter').addEventListener('change', () => this.filterTransactions());
        
        // Export functionality
        document.getElementById('exportBtn').addEventListener('click', () => this.exportData());

        // Clear data functionality
        document.getElementById('clearDataBtn').addEventListener('click', () => this.clearAllData());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        // Close modal when clicking outside
        document.getElementById('transactionModal').addEventListener('click', (e) => {
            if (e.target.id === 'transactionModal') {
                this.closeModal();
            }
        });

        // Column sorting
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => {
                const column = header.dataset.column;
                this.sortTransactions(column);
            });
        });
    }

    // Setup tab navigation
    setupTabNavigation() {
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                this.switchTab(tabName);
            });
        });
    }

    // Switch between tabs
    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        // Update summary if switching to summary tab
        if (tabName === 'summary') {
            this.updateSummaryDashboard();
        }
    }

    // Load transactions from localStorage
    loadTransactions() {
        const stored = localStorage.getItem('financeTransactions');
        if (stored) {
            return JSON.parse(stored);
        } else {
            // Return sample data for first-time users
            return this.getSampleData();
        }
    }

    // Get sample data for demonstration
    getSampleData() {
        const today = new Date();
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        const twoWeeksAgo = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 14);

        return [
            {
                id: '1',
                date: today.toISOString().split('T')[0],
                type: 'credit',
                amount: 3500.00,
                comment: 'Salary deposit',
                tags: 'Both'
            },
            {
                id: '2',
                date: twoWeeksAgo.toISOString().split('T')[0],
                type: 'debit',
                amount: 1200.00,
                comment: 'Rent payment',
                tags: 'Both'
            },
            {
                id: '3',
                date: lastMonth.toISOString().split('T')[0],
                type: 'debit',
                amount: 85.50,
                comment: 'Grocery shopping',
                tags: 'Nikita'
            },
            {
                id: '4',
                date: lastMonth.toISOString().split('T')[0],
                type: 'debit',
                amount: 45.00,
                comment: 'Gas station',
                tags: 'Lance'
            },
            {
                id: '5',
                date: new Date(today.getFullYear(), today.getMonth(), today.getDate() - 3).toISOString().split('T')[0],
                type: 'credit',
                amount: 250.00,
                comment: 'Freelance work',
                tags: 'Lance'
            }
        ];
    }

    // Save transactions to localStorage
    saveTransactions() {
        localStorage.setItem('financeTransactions', JSON.stringify(this.transactions));
    }

    // Open modal for adding/editing transaction
    openModal(transaction = null) {
        const modal = document.getElementById('transactionModal');
        const form = document.getElementById('transactionForm');
        const title = document.getElementById('modalTitle');

        if (transaction) {
            // Edit mode
            title.textContent = 'Edit Transaction';
            this.currentEditId = transaction.id;
            
            document.getElementById('transactionDate').value = transaction.date;
            document.getElementById('transactionType').value = transaction.type;
            document.getElementById('transactionAmount').value = transaction.amount;
            document.getElementById('transactionComment').value = transaction.comment;
            document.getElementById('transactionTags').value = transaction.tags;
        } else {
            // Add mode
            title.textContent = 'Add Transaction';
            this.currentEditId = null;
            form.reset();
            
            // Set today's date as default
            document.getElementById('transactionDate').value = new Date().toISOString().split('T')[0];
        }

        modal.style.display = 'block';
    }

    // Close modal
    closeModal() {
        document.getElementById('transactionModal').style.display = 'none';
        this.currentEditId = null;
    }

    // Handle form submission
    handleFormSubmit(e) {
        e.preventDefault();

        // Validate form data
        const validation = this.validateFormData();
        if (!validation.isValid) {
            this.showValidationErrors(validation.errors);
            return;
        }

        const formData = {
            date: document.getElementById('transactionDate').value,
            type: document.getElementById('transactionType').value,
            amount: parseFloat(document.getElementById('transactionAmount').value),
            comment: document.getElementById('transactionComment').value.trim(),
            tags: document.getElementById('transactionTags').value
        };

        if (this.currentEditId) {
            // Update existing transaction
            const index = this.transactions.findIndex(t => t.id === this.currentEditId);
            if (index !== -1) {
                this.transactions[index] = { ...formData, id: this.currentEditId };
            }
        } else {
            // Add new transaction
            const newTransaction = {
                ...formData,
                id: Date.now().toString()
            };
            this.transactions.push(newTransaction);
        }

        this.saveTransactions();
        this.renderTransactions();
        this.updateSummaryStats();
        this.closeModal();
        this.showSuccessMessage(this.currentEditId ? 'Transaction updated successfully!' : 'Transaction added successfully!');
    }

    // Validate form data
    validateFormData() {
        const errors = [];

        const date = document.getElementById('transactionDate').value;
        const type = document.getElementById('transactionType').value;
        const amount = document.getElementById('transactionAmount').value;
        const tags = document.getElementById('transactionTags').value;

        // Date validation
        if (!date) {
            errors.push('Date is required');
        } else {
            const selectedDate = new Date(date);
            const today = new Date();
            const oneYearFromNow = new Date();
            oneYearFromNow.setFullYear(today.getFullYear() + 1);

            if (selectedDate > oneYearFromNow) {
                errors.push('Date cannot be more than one year in the future');
            }
        }

        // Type validation
        if (!type) {
            errors.push('Transaction type is required');
        }

        // Amount validation
        if (!amount) {
            errors.push('Amount is required');
        } else {
            const numAmount = parseFloat(amount);
            if (isNaN(numAmount) || numAmount <= 0) {
                errors.push('Amount must be a positive number');
            } else if (numAmount > 1000000) {
                errors.push('Amount cannot exceed $1,000,000');
            }
        }

        // Tags validation
        if (!tags) {
            errors.push('Tag is required');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // Show validation errors
    showValidationErrors(errors) {
        this.removeExistingMessages();

        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-errors';
        errorDiv.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Please fix the following errors:</strong>
                <ul>
                    ${errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
            </div>
        `;

        const form = document.getElementById('transactionForm');
        form.insertBefore(errorDiv, form.firstChild);
    }

    // Show success message
    showSuccessMessage(message) {
        this.removeExistingMessages();

        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.innerHTML = `
            <div class="success-content">
                <i class="fas fa-check-circle"></i>
                ${message}
            </div>
        `;

        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(successDiv, mainContent.firstChild);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    }

    // Remove existing messages
    removeExistingMessages() {
        const existingErrors = document.querySelector('.validation-errors');
        const existingSuccess = document.querySelector('.success-message');

        if (existingErrors) {
            existingErrors.parentNode.removeChild(existingErrors);
        }
        if (existingSuccess) {
            existingSuccess.parentNode.removeChild(existingSuccess);
        }
    }

    // Delete transaction
    deleteTransaction(id) {
        if (confirm('Are you sure you want to delete this transaction?')) {
            this.transactions = this.transactions.filter(t => t.id !== id);
            this.saveTransactions();
            this.renderTransactions();
            this.updateSummaryStats();
        }
    }

    // Sort transactions
    sortTransactions(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }

        this.transactions.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            // Handle different data types
            if (column === 'date') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            } else if (column === 'amount') {
                aVal = parseFloat(aVal);
                bVal = parseFloat(bVal);
            } else {
                aVal = aVal.toString().toLowerCase();
                bVal = bVal.toString().toLowerCase();
            }

            if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

        this.renderTransactions();
        this.updateSortIcons();
    }

    // Update sort icons
    updateSortIcons() {
        document.querySelectorAll('.sortable i').forEach(icon => {
            icon.className = 'fas fa-sort';
        });

        const activeHeader = document.querySelector(`[data-column="${this.sortColumn}"] i`);
        if (activeHeader) {
            activeHeader.className = this.sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
        }
    }

    // Filter transactions
    filterTransactions() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const tagFilter = document.getElementById('tagFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;

        let filtered = this.transactions.filter(transaction => {
            const matchesSearch = !searchTerm || 
                transaction.comment.toLowerCase().includes(searchTerm) ||
                transaction.type.toLowerCase().includes(searchTerm) ||
                transaction.tags.toLowerCase().includes(searchTerm);

            const matchesTag = !tagFilter || transaction.tags === tagFilter;
            const matchesDate = !dateFilter || transaction.date === dateFilter;

            return matchesSearch && matchesTag && matchesDate;
        });

        this.renderTransactions(filtered);
    }

    // Render transactions in the grid
    renderTransactions(transactionsToRender = null) {
        const transactions = transactionsToRender || this.transactions;
        const tbody = document.getElementById('transactionTableBody');
        
        tbody.innerHTML = '';

        transactions.forEach(transaction => {
            const row = document.createElement('tr');
            
            const formattedDate = new Date(transaction.date).toLocaleDateString();
            const formattedAmount = `$${transaction.amount.toFixed(2)}`;
            
            row.innerHTML = `
                <td>${formattedDate}</td>
                <td><span class="type-${transaction.type}">${transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}</span></td>
                <td><span class="amount-${transaction.type}">${formattedAmount}</span></td>
                <td>${transaction.comment}</td>
                <td><span class="tag tag-${transaction.tags.toLowerCase()}">${transaction.tags}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" onclick="dashboard.openModal(${JSON.stringify(transaction).replace(/"/g, '&quot;')})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="dashboard.deleteTransaction('${transaction.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    }

    // Update summary statistics
    updateSummaryStats() {
        const totalIncome = this.transactions
            .filter(t => t.type === 'credit')
            .reduce((sum, t) => sum + t.amount, 0);

        const totalExpenses = this.transactions
            .filter(t => t.type === 'debit')
            .reduce((sum, t) => sum + t.amount, 0);

        const netAmount = totalIncome - totalExpenses;

        document.getElementById('totalIncome').textContent = `$${totalIncome.toFixed(2)}`;
        document.getElementById('totalExpenses').textContent = `$${totalExpenses.toFixed(2)}`;
        
        const netElement = document.getElementById('netAmount');
        netElement.textContent = `$${netAmount.toFixed(2)}`;
        netElement.className = `stat-value ${netAmount >= 0 ? 'income' : 'expense'}`;
    }

    // Update summary dashboard
    updateSummaryDashboard() {
        this.updateTagSummary();
        this.updateMonthlySummary();
    }

    // Update tag summary
    updateTagSummary() {
        const tagSummary = {};
        
        this.transactions.forEach(transaction => {
            if (!tagSummary[transaction.tags]) {
                tagSummary[transaction.tags] = { income: 0, expenses: 0, net: 0 };
            }
            
            if (transaction.type === 'credit') {
                tagSummary[transaction.tags].income += transaction.amount;
            } else {
                tagSummary[transaction.tags].expenses += transaction.amount;
            }
            
            tagSummary[transaction.tags].net = tagSummary[transaction.tags].income - tagSummary[transaction.tags].expenses;
        });

        const container = document.getElementById('tagSummary');
        container.innerHTML = '';

        Object.entries(tagSummary).forEach(([tag, data]) => {
            const tagDiv = document.createElement('div');
            tagDiv.style.marginBottom = '1rem';
            tagDiv.innerHTML = `
                <h4>${tag}</h4>
                <p>Income: <span class="income">$${data.income.toFixed(2)}</span></p>
                <p>Expenses: <span class="expense">$${data.expenses.toFixed(2)}</span></p>
                <p>Net: <span class="${data.net >= 0 ? 'income' : 'expense'}">$${data.net.toFixed(2)}</span></p>
                <hr style="margin: 0.5rem 0;">
            `;
            container.appendChild(tagDiv);
        });
    }

    // Update monthly summary
    updateMonthlySummary() {
        const monthlySummary = {};
        
        this.transactions.forEach(transaction => {
            const month = transaction.date.substring(0, 7); // YYYY-MM format
            
            if (!monthlySummary[month]) {
                monthlySummary[month] = { income: 0, expenses: 0, net: 0 };
            }
            
            if (transaction.type === 'credit') {
                monthlySummary[month].income += transaction.amount;
            } else {
                monthlySummary[month].expenses += transaction.amount;
            }
            
            monthlySummary[month].net = monthlySummary[month].income - monthlySummary[month].expenses;
        });

        const container = document.getElementById('monthlySummary');
        container.innerHTML = '';

        // Sort months in descending order
        const sortedMonths = Object.keys(monthlySummary).sort().reverse();

        sortedMonths.forEach(month => {
            const data = monthlySummary[month];
            const monthDiv = document.createElement('div');
            monthDiv.style.marginBottom = '1rem';
            
            const monthName = new Date(month + '-01').toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long' 
            });
            
            monthDiv.innerHTML = `
                <h4>${monthName}</h4>
                <p>Income: <span class="income">$${data.income.toFixed(2)}</span></p>
                <p>Expenses: <span class="expense">$${data.expenses.toFixed(2)}</span></p>
                <p>Net: <span class="${data.net >= 0 ? 'income' : 'expense'}">$${data.net.toFixed(2)}</span></p>
                <hr style="margin: 0.5rem 0;">
            `;
            container.appendChild(monthDiv);
        });
    }

    // Handle keyboard shortcuts
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + N: Add new transaction
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            this.openModal();
        }

        // Escape: Close modal
        if (e.key === 'Escape') {
            this.closeModal();
        }

        // Ctrl/Cmd + E: Export data
        if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
            e.preventDefault();
            this.exportData();
        }
    }

    // Export data to CSV
    exportData() {
        if (this.transactions.length === 0) {
            alert('No transactions to export!');
            return;
        }

        const headers = ['Date', 'Type', 'Amount', 'Comment', 'Tags'];
        const csvContent = [
            headers.join(','),
            ...this.transactions.map(t => [
                t.date,
                t.type,
                t.amount,
                `"${t.comment}"`,
                t.tags
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `finance_transactions_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);

        this.showSuccessMessage('Data exported successfully!');
    }

    // Clear all data (with confirmation)
    clearAllData() {
        if (confirm('Are you sure you want to delete ALL transactions? This action cannot be undone.')) {
            if (confirm('This will permanently delete all your financial data. Are you absolutely sure?')) {
                this.transactions = [];
                this.saveTransactions();
                this.renderTransactions();
                this.updateSummaryStats();
                this.showSuccessMessage('All data cleared successfully!');
            }
        }
    }
}

// Initialize the dashboard when the page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new FinanceDashboard();
});
