/* CSS Variables for Theme Support */
:root {
    /* Light Theme Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f5f5f5;
    --bg-tertiary: #f8f9fa;
    --bg-accent: #e9ecef;
    --text-primary: #333333;
    --text-secondary: #6c757d;
    --text-muted: #495057;
    --border-color: #dee2e6;
    --border-light: #e5e7eb;
    --shadow: rgba(0,0,0,0.1);
    --shadow-dark: rgba(0,0,0,0.3);
    --gradient-start: #667eea;
    --gradient-end: #764ba2;
    --success: #28a745;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #17a2b8;
}

[data-theme="dark"] {
    /* Dark Theme Colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --bg-accent: #4a4a4a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #888888;
    --border-color: #555555;
    --border-light: #444444;
    --shadow: rgba(0,0,0,0.3);
    --shadow-dark: rgba(0,0,0,0.6);
    --gradient-start: #4a5568;
    --gradient-end: #2d3748;
    --success: #48bb78;
    --danger: #f56565;
    --warning: #ed8936;
    --info: #4299e1;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: var(--bg-primary);
    min-height: 100vh;
    box-shadow: 0 0 20px var(--shadow);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 1.8rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--success);
    color: white;
}

.btn-primary:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--text-secondary);
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: var(--danger);
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.btn-success {
    background: var(--success);
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn-warning {
    background: var(--warning);
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-2px);
}

.btn-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem;
    min-width: auto;
    border-radius: 50%;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: var(--bg-tertiary);
    border-bottom: 2px solid var(--border-color);
}

.nav-tab {
    padding: 1rem 2rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-tab:hover {
    background: var(--bg-accent);
    color: var(--text-muted);
}

.nav-tab.active {
    background: var(--bg-primary);
    color: var(--gradient-start);
    border-bottom: 3px solid var(--gradient-start);
}

/* Main Content */
.main-content {
    padding: 2rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Toolbar */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: 8px;
    flex-wrap: wrap;
    gap: 1rem;
}

.search-filter {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-input, .filter-select, .filter-date {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: border-color 0.3s ease, background-color 0.3s ease;
}

.search-input:focus, .filter-select:focus, .filter-date:focus {
    outline: none;
    border-color: var(--gradient-start);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input {
    min-width: 200px;
}

/* Grid Controls */
.grid-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.edit-mode-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.summary-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 600;
}

.stat-value.income {
    color: var(--success);
}

.stat-value.expense {
    color: var(--danger);
}

/* Transaction Grid - Excel-like styling */
.grid-container {
    overflow-x: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
}

.transaction-grid {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.transaction-grid th {
    background: var(--bg-tertiary);
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid var(--border-color);
    border-right: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    cursor: pointer;
    user-select: none;
    color: var(--text-primary);
}

.transaction-grid th:hover {
    background: var(--bg-accent);
}

.transaction-grid th.sortable {
    position: relative;
}

.transaction-grid th i {
    margin-left: 0.5rem;
    opacity: 0.5;
}

.transaction-grid td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-light);
    border-right: 1px solid var(--border-light);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.transaction-grid tbody tr:hover {
    background: var(--bg-tertiary);
}

.transaction-grid tbody tr:nth-child(even) {
    background: var(--bg-secondary);
}

.transaction-grid tbody tr:nth-child(even):hover {
    background: var(--bg-accent);
}

/* Inline Edit Styles */
.transaction-grid .edit-row {
    background: var(--bg-accent) !important;
}

.transaction-grid .edit-input {
    width: 100%;
    padding: 4px 8px;
    border: 1px solid var(--gradient-start);
    border-radius: 3px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.transaction-grid .edit-select {
    width: 100%;
    padding: 4px 8px;
    border: 1px solid var(--gradient-start);
    border-radius: 3px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.transaction-grid .edit-input:focus,
.transaction-grid .edit-select:focus {
    outline: none;
    border-color: var(--gradient-start);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Transaction Type Styling */
.type-credit {
    color: var(--success);
    font-weight: 600;
}

.type-debit {
    color: var(--danger);
    font-weight: 600;
}

.amount-credit {
    color: var(--success);
    font-weight: 600;
}

.amount-debit {
    color: var(--danger);
    font-weight: 600;
}

/* Tags */
.tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: #e9ecef;
    color: #495057;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.tag.tag-nikita {
    background: #e3f2fd;
    color: #1976d2;
}

.tag.tag-lance {
    background: #f3e5f5;
    color: #7b1fa2;
}

.tag.tag-both {
    background: #e8f5e8;
    color: #388e3c;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.edit-btn {
    background: #ffc107;
    color: #212529;
}

.edit-btn:hover {
    background: #e0a800;
}

.delete-btn {
    background: var(--danger);
    color: white;
}

.delete-btn:hover {
    background: #c82333;
}

.btn-success {
    background: var(--success);
    color: white;
    font-size: 0.8rem;
}

.btn-success:hover {
    background: #218838;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: var(--shadow-dark);
}

.modal-content {
    background-color: var(--bg-primary);
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px var(--shadow-dark);
}

.modal-header {
    padding: 1.5rem 2rem;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: var(--text-primary);
}

/* Form Styles */
#transactionForm {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: border-color 0.3s ease, background-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--gradient-start);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

/* Summary Dashboard */
.summary-dashboard h2 {
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.summary-card {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px var(--shadow);
    border: 1px solid var(--border-color);
}

.summary-card h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    border-bottom: 2px solid var(--gradient-start);
    padding-bottom: 0.5rem;
}

/* Validation and Success Messages */
.validation-errors {
    margin-bottom: 1.5rem;
}

.error-message {
    background: rgba(248, 215, 218, 0.9);
    color: #721c24;
    padding: 1rem;
    border-radius: 4px;
    border: 1px solid rgba(245, 198, 203, 0.9);
}

[data-theme="dark"] .error-message {
    background: rgba(220, 53, 69, 0.2);
    color: #f8d7da;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.error-message i {
    margin-right: 0.5rem;
}

.error-message ul {
    margin: 0.5rem 0 0 1.5rem;
}

.error-message li {
    margin-bottom: 0.25rem;
}

.success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    animation: slideInRight 0.3s ease-out;
}

.success-content {
    background: rgba(212, 237, 218, 0.95);
    color: #155724;
    padding: 1rem 1.5rem;
    border-radius: 4px;
    border: 1px solid rgba(195, 230, 203, 0.95);
    box-shadow: 0 4px 12px var(--shadow);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

[data-theme="dark"] .success-content {
    background: rgba(72, 187, 120, 0.2);
    color: #d4edda;
    border: 1px solid rgba(72, 187, 120, 0.3);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced form validation styles */
.form-group input:invalid,
.form-group select:invalid {
    border-color: #dc3545;
}

.form-group input:valid,
.form-group select:valid {
    border-color: #28a745;
}

/* Loading states */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }

    .search-filter {
        justify-content: center;
    }

    .summary-stats {
        justify-content: center;
    }

    .transaction-grid {
        font-size: 0.8rem;
    }

    .transaction-grid th,
    .transaction-grid td {
        padding: 8px 12px;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
    }

    .success-message {
        top: 10px;
        right: 10px;
        left: 10px;
    }

    .success-content {
        text-align: center;
    }
}
