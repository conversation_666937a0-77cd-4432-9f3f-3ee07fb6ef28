/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 1.8rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
}

.nav-tab {
    padding: 1rem 2rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    color: #6c757d;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-tab:hover {
    background: #e9ecef;
    color: #495057;
}

.nav-tab.active {
    background: white;
    color: #667eea;
    border-bottom: 3px solid #667eea;
}

/* Main Content */
.main-content {
    padding: 2rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Toolbar */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    flex-wrap: wrap;
    gap: 1rem;
}

.search-filter {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-input, .filter-select, .filter-date {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.search-input {
    min-width: 200px;
}

.summary-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 600;
}

.stat-value.income {
    color: #28a745;
}

.stat-value.expense {
    color: #dc3545;
}

/* Transaction Grid - Excel-like styling */
.grid-container {
    overflow-x: auto;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: white;
}

.transaction-grid {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.transaction-grid th {
    background: #f3f4f6;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #d1d5db;
    border-right: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    cursor: pointer;
    user-select: none;
}

.transaction-grid th:hover {
    background: #e5e7eb;
}

.transaction-grid th.sortable {
    position: relative;
}

.transaction-grid th i {
    margin-left: 0.5rem;
    opacity: 0.5;
}

.transaction-grid td {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    border-right: 1px solid #e5e7eb;
    background: white;
}

.transaction-grid tbody tr:hover {
    background: #f9fafb;
}

.transaction-grid tbody tr:nth-child(even) {
    background: #fafafa;
}

.transaction-grid tbody tr:nth-child(even):hover {
    background: #f0f0f0;
}

/* Transaction Type Styling */
.type-credit {
    color: #28a745;
    font-weight: 600;
}

.type-debit {
    color: #dc3545;
    font-weight: 600;
}

.amount-credit {
    color: #28a745;
    font-weight: 600;
}

.amount-debit {
    color: #dc3545;
    font-weight: 600;
}

/* Tags */
.tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: #e9ecef;
    color: #495057;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.tag.tag-nikita {
    background: #e3f2fd;
    color: #1976d2;
}

.tag.tag-lance {
    background: #f3e5f5;
    color: #7b1fa2;
}

.tag.tag-both {
    background: #e8f5e8;
    color: #388e3c;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.edit-btn {
    background: #ffc107;
    color: #212529;
}

.edit-btn:hover {
    background: #e0a800;
}

.delete-btn {
    background: #dc3545;
    color: white;
}

.delete-btn:hover {
    background: #c82333;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 1.5rem 2rem;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #495057;
}

/* Form Styles */
#transactionForm {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #495057;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

/* Summary Dashboard */
.summary-dashboard h2 {
    margin-bottom: 2rem;
    color: #495057;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.summary-card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.summary-card h3 {
    margin-bottom: 1rem;
    color: #495057;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

/* Validation and Success Messages */
.validation-errors {
    margin-bottom: 1.5rem;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
}

.error-message i {
    margin-right: 0.5rem;
}

.error-message ul {
    margin: 0.5rem 0 0 1.5rem;
}

.error-message li {
    margin-bottom: 0.25rem;
}

.success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    animation: slideInRight 0.3s ease-out;
}

.success-content {
    background: #d4edda;
    color: #155724;
    padding: 1rem 1.5rem;
    border-radius: 4px;
    border: 1px solid #c3e6cb;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced form validation styles */
.form-group input:invalid,
.form-group select:invalid {
    border-color: #dc3545;
}

.form-group input:valid,
.form-group select:valid {
    border-color: #28a745;
}

/* Loading states */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }

    .search-filter {
        justify-content: center;
    }

    .summary-stats {
        justify-content: center;
    }

    .transaction-grid {
        font-size: 0.8rem;
    }

    .transaction-grid th,
    .transaction-grid td {
        padding: 8px 12px;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
    }

    .success-message {
        top: 10px;
        right: 10px;
        left: 10px;
    }

    .success-content {
        text-align: center;
    }
}
